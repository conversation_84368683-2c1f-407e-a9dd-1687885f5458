<?php
/**
 * DCIM WHMCS Hooks
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use Illuminate\Database\Capsule\Manager as Capsule;

/**
 * Hook: AfterModuleCreate
 * Automatically assign IP address when a new service is created
 */
add_hook('AfterModuleCreate', 1, function($vars) {
    // Check if auto IP allocation is enabled
    $autoIPAllocation = Capsule::table('tbladdonmodules')
        ->where('module', 'dcim')
        ->where('setting', 'auto_ip_allocation')
        ->where('value', 'on')
        ->exists();
    
    if (!$autoIPAllocation) {
        return;
    }
    
    try {
        // Get service details
        $serviceId = $vars['serviceid'];
        $clientId = $vars['userid'];
        $productId = $vars['productid'];
        
        // Check if product has DCIM configuration
        $productConfig = Capsule::table('tblproducts')
            ->where('id', $productId)
            ->first();
        
        if (!$productConfig) {
            return;
        }
        
        // Find available IP from default subnet
        $availableIP = Capsule::table('dcim_ip_assignments')
            ->join('dcim_subnets', 'dcim_ip_assignments.subnet_id', '=', 'dcim_subnets.id')
            ->where('dcim_ip_assignments.status', 'available')
            ->where('dcim_subnets.status', 'active')
            ->first();
        
        if ($availableIP) {
            // Assign IP to service
            Capsule::table('dcim_ip_assignments')
                ->where('id', $availableIP->id)
                ->update([
                    'assignment_type' => 'service',
                    'assigned_to_id' => $serviceId,
                    'whmcs_client_id' => $clientId,
                    'whmcs_service_id' => $serviceId,
                    'status' => 'active',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            // Log the assignment
            logActivity("DCIM: Automatically assigned IP {$availableIP->ip_address} to service ID {$serviceId}");
        }
        
    } catch (Exception $e) {
        logActivity("DCIM Hook Error: " . $e->getMessage());
    }
});

/**
 * Hook: AfterModuleTerminate
 * Release IP address when service is terminated
 */
add_hook('AfterModuleTerminate', 1, function($vars) {
    try {
        $serviceId = $vars['serviceid'];
        
        // Find and release IP assignments for this service
        $assignments = Capsule::table('dcim_ip_assignments')
            ->where('whmcs_service_id', $serviceId)
            ->get();
        
        foreach ($assignments as $assignment) {
            Capsule::table('dcim_ip_assignments')
                ->where('id', $assignment->id)
                ->update([
                    'assignment_type' => 'available',
                    'assigned_to_id' => null,
                    'whmcs_client_id' => null,
                    'whmcs_service_id' => null,
                    'hostname' => null,
                    'description' => 'Released from terminated service',
                    'status' => 'available',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            logActivity("DCIM: Released IP {$assignment->ip_address} from terminated service ID {$serviceId}");
        }
        
    } catch (Exception $e) {
        logActivity("DCIM Hook Error: " . $e->getMessage());
    }
});

/**
 * Hook: ClientAreaPrimaryNavbar
 * Add DCIM menu items to client area (if needed)
 */
add_hook('ClientAreaPrimaryNavbar', 1, function($primaryNavbar) {
    // Check if client has any services with IP assignments
    $clientId = $_SESSION['uid'] ?? 0;
    
    if (!$clientId) {
        return;
    }
    
    try {
        $hasIPAssignments = Capsule::table('dcim_ip_assignments')
            ->where('whmcs_client_id', $clientId)
            ->where('status', 'active')
            ->exists();
        
        if ($hasIPAssignments) {
            $primaryNavbar->addChild('dcim', [
                'label' => 'IP Management',
                'uri' => 'clientarea.php?action=productdetails&dcim=ips',
                'order' => 60,
            ]);
        }
        
    } catch (Exception $e) {
        // Silently fail
    }
});

/**
 * Hook: AdminAreaClientSummaryPage
 * Add DCIM information to client summary page
 */
add_hook('AdminAreaClientSummaryPage', 1, function($vars) {
    $clientId = $vars['userid'];
    
    try {
        // Get client's IP assignments
        $ipAssignments = Capsule::table('dcim_ip_assignments')
            ->join('dcim_subnets', 'dcim_ip_assignments.subnet_id', '=', 'dcim_subnets.id')
            ->where('dcim_ip_assignments.whmcs_client_id', $clientId)
            ->select([
                'dcim_ip_assignments.ip_address',
                'dcim_ip_assignments.hostname',
                'dcim_ip_assignments.status',
                'dcim_subnets.name as subnet_name'
            ])
            ->get();
        
        if ($ipAssignments->count() > 0) {
            $output = '<div class="panel panel-default">';
            $output .= '<div class="panel-heading"><h3 class="panel-title">DCIM - IP Assignments</h3></div>';
            $output .= '<div class="panel-body">';
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-condensed">';
            $output .= '<thead><tr><th>IP Address</th><th>Hostname</th><th>Subnet</th><th>Status</th></tr></thead>';
            $output .= '<tbody>';
            
            foreach ($ipAssignments as $ip) {
                $statusClass = $ip->status == 'active' ? 'success' : 'default';
                $output .= '<tr>';
                $output .= '<td><code>' . htmlspecialchars($ip->ip_address) . '</code></td>';
                $output .= '<td>' . htmlspecialchars($ip->hostname ?? '-') . '</td>';
                $output .= '<td>' . htmlspecialchars($ip->subnet_name) . '</td>';
                $output .= '<td><span class="label label-' . $statusClass . '">' . ucfirst($ip->status) . '</span></td>';
                $output .= '</tr>';
            }
            
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
            $output .= '<div class="text-right">';
            $output .= '<a href="addonmodules.php?module=dcim&action=ipam&client_id=' . $clientId . '" class="btn btn-sm btn-primary">Manage IPs</a>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
            
            return $output;
        }
        
    } catch (Exception $e) {
        return '<div class="alert alert-warning">DCIM: Unable to load IP assignments</div>';
    }
    
    return '';
});

/**
 * Hook: AdminAreaClientServicesTabFields
 * Add DCIM fields to service details
 */
add_hook('AdminAreaClientServicesTabFields', 1, function($vars) {
    $serviceId = $vars['serviceid'];
    
    try {
        // Get server assignment for this service
        $server = Capsule::table('dcim_servers')
            ->join('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->where('dcim_servers.whmcs_service_id', $serviceId)
            ->select([
                'dcim_servers.hostname',
                'dcim_servers.status as server_status',
                'dcim_racks.name as rack_name',
                'dcim_locations.name as location_name'
            ])
            ->first();
        
        // Get IP assignments for this service
        $ipAssignments = Capsule::table('dcim_ip_assignments')
            ->where('whmcs_service_id', $serviceId)
            ->get();
        
        $fieldsArray = [];
        
        if ($server) {
            $fieldsArray['DCIM Server'] = $server->hostname . ' (' . ucfirst($server->server_status) . ')';
            $fieldsArray['DCIM Location'] = $server->location_name . ' - ' . $server->rack_name;
        }
        
        if ($ipAssignments->count() > 0) {
            $ips = [];
            foreach ($ipAssignments as $ip) {
                $ips[] = $ip->ip_address . ' (' . ucfirst($ip->status) . ')';
            }
            $fieldsArray['DCIM IP Addresses'] = implode('<br>', $ips);
        }
        
        return $fieldsArray;
        
    } catch (Exception $e) {
        return ['DCIM Error' => 'Unable to load DCIM information'];
    }
});

/**
 * Hook: DailyCronJob
 * Daily maintenance tasks
 */
add_hook('DailyCronJob', 1, function($vars) {
    try {
        // Clean up old activity logs (if implemented)
        // Update rack utilization statistics
        $racks = Capsule::table('dcim_racks')->get();
        
        foreach ($racks as $rack) {
            $serverCount = Capsule::table('dcim_servers')
                ->where('rack_id', $rack->id)
                ->where('status', '!=', 'decommissioned')
                ->count();
            
            $powerUsage = Capsule::table('dcim_servers')
                ->where('rack_id', $rack->id)
                ->where('status', 'active')
                ->sum('power_consumption');
            
            Capsule::table('dcim_racks')
                ->where('id', $rack->id)
                ->update([
                    'power_usage' => $powerUsage ?? 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
        
        logActivity("DCIM: Daily maintenance completed");
        
    } catch (Exception $e) {
        logActivity("DCIM Daily Cron Error: " . $e->getMessage());
    }
});
