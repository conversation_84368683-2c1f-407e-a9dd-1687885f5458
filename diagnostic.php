<?php
/**
 * DCIM Diagnostic Page
 * 
 * This page will help us debug why the module pages appear empty
 * Access via: https://your-domain.com/whmcs/modules/addons/dcim/diagnostic.php
 */

// Include WHMCS
$whmcsPath = dirname(dirname(dirname(__DIR__)));
require_once $whmcsPath . '/init.php';

// Check if user is logged in as admin
if (!isset($_SESSION['adminid'])) {
    die('Please log in to WHMCS admin first, then access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>DCIM Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 15px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .output-box { border: 2px solid #007bff; padding: 15px; background: #f8f9fa; margin: 10px 0; max-height: 400px; overflow: auto; }
        pre { background: #f4f4f4; padding: 10px; overflow: auto; }
    </style>
</head>
<body>

<h1>DCIM Module Diagnostic</h1>

<div class="test-section">
    <h2>1. Module Output Test</h2>
    <?php
    try {
        // Include the module
        require_once __DIR__ . '/dcim.php';
        
        // Simulate WHMCS module call with proper vars
        $moduleVars = [
            'modulelink' => '/whmcs/admin/addonmodules.php?module=dcim',
            'version' => '1.0.0',
            'LANG' => []
        ];
        
        echo "<p class='success'>✓ Module loaded successfully</p>";
        
        // Get the output
        $output = dcim_output($moduleVars);
        
        echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
        
        if (!empty($output)) {
            echo "<p class='success'>✓ Module generates content</p>";
            echo "<h3>Raw HTML Output:</h3>";
            echo "<div class='output-box'>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            echo "</div>";
            
            echo "<h3>Rendered Output:</h3>";
            echo "<div class='output-box'>";
            echo $output;
            echo "</div>";
        } else {
            echo "<p class='error'>✗ Module returns empty output</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    ?>
</div>

<div class="test-section">
    <h2>2. Direct Controller Test</h2>
    <?php
    try {
        require_once __DIR__ . '/lib/AdminController.php';
        
        $vars = [
            'modulelink' => '/whmcs/admin/addonmodules.php?module=dcim',
            'version' => '1.0.0'
        ];
        
        $controller = new DCIM\AdminController($vars);
        echo "<p class='success'>✓ AdminController created</p>";
        
        $controllerOutput = $controller->handleRequest();
        echo "<p><strong>Controller output length:</strong> " . strlen($controllerOutput) . " characters</p>";
        
        if (!empty($controllerOutput)) {
            echo "<p class='success'>✓ Controller generates content</p>";
            echo "<h3>Controller Raw Output:</h3>";
            echo "<div class='output-box'>";
            echo "<pre>" . htmlspecialchars($controllerOutput) . "</pre>";
            echo "</div>";
            
            echo "<h3>Controller Rendered Output:</h3>";
            echo "<div class='output-box'>";
            echo $controllerOutput;
            echo "</div>";
        } else {
            echo "<p class='error'>✗ Controller returns empty output</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Controller Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    ?>
</div>

<div class="test-section">
    <h2>3. Asset Loading Test</h2>
    <?php
    $cssPath = __DIR__ . '/assets/css/dcim.css';
    $jsPath = __DIR__ . '/assets/js/dcim.js';
    
    if (file_exists($cssPath)) {
        echo "<p class='success'>✓ CSS file exists</p>";
        echo "<p>CSS file size: " . filesize($cssPath) . " bytes</p>";
    } else {
        echo "<p class='error'>✗ CSS file missing</p>";
    }
    
    if (file_exists($jsPath)) {
        echo "<p class='success'>✓ JS file exists</p>";
        echo "<p>JS file size: " . filesize($jsPath) . " bytes</p>";
    } else {
        echo "<p class='error'>✗ JS file missing</p>";
    }
    
    // Test asset URLs
    echo "<h3>Asset URLs:</h3>";
    echo "<p>CSS URL: <a href='/whmcs/modules/addons/dcim/assets/css/dcim.css' target='_blank'>/whmcs/modules/addons/dcim/assets/css/dcim.css</a></p>";
    echo "<p>JS URL: <a href='/whmcs/modules/addons/dcim/assets/js/dcim.js' target='_blank'>/whmcs/modules/addons/dcim/assets/js/dcim.js</a></p>";
    ?>
</div>

<div class="test-section">
    <h2>4. Browser Console Check</h2>
    <p>Open your browser's developer tools (F12) and check the Console tab for any JavaScript errors.</p>
    <p>Also check the Network tab to see if CSS/JS files are loading properly.</p>
    
    <script>
    console.log("DCIM Diagnostic: JavaScript is working");
    
    // Check if jQuery is available
    if (typeof jQuery !== 'undefined') {
        console.log("DCIM Diagnostic: jQuery is available");
    } else {
        console.log("DCIM Diagnostic: jQuery is NOT available");
    }
    
    // Check for any obvious issues
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DCIM Diagnostic: DOM loaded");
        
        // Check if Bootstrap is available
        if (typeof bootstrap !== 'undefined' || typeof $ !== 'undefined' && typeof $.fn.modal !== 'undefined') {
            console.log("DCIM Diagnostic: Bootstrap appears to be available");
        } else {
            console.log("DCIM Diagnostic: Bootstrap may not be available");
        }
    });
    </script>
</div>

<div class="test-section">
    <h2>5. WHMCS Integration Test</h2>
    <p>Try accessing the module through WHMCS:</p>
    <ul>
        <li><a href="/whmcs/admin/addonmodules.php?module=dcim" target="_blank">DCIM Dashboard (Direct Link)</a></li>
        <li><a href="/whmcs/admin/addonmodules.php?module=dcim&action=locations" target="_blank">Locations (Direct Link)</a></li>
    </ul>
    
    <p><strong>Instructions:</strong></p>
    <ol>
        <li>Click the links above</li>
        <li>If pages are still empty, right-click and "View Page Source"</li>
        <li>Look for the DCIM content in the HTML source</li>
        <li>Check browser developer tools for errors</li>
    </ol>
</div>

<div class="test-section">
    <h2>6. Simple Test Output</h2>
    <p>This is a simple HTML test to ensure basic rendering works:</p>
    <div style="border: 2px solid red; padding: 10px; background: yellow;">
        <h3>Test Content</h3>
        <p>If you can see this yellow box with red border, HTML rendering is working.</p>
        <button onclick="alert('JavaScript is working!')">Test JavaScript</button>
    </div>
</div>

</body>
</html>
