<?php
/**
 * DCIM Language File - English
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

$_LANG['dcim'] = [
    // General
    'module_name' => 'DCIM - Data Center Infrastructure Management',
    'dashboard' => 'Dashboard',
    'locations' => 'Locations',
    'racks' => 'Racks',
    'servers' => 'Servers',
    'switches' => 'Switches',
    'ipam' => 'IP Management',
    'api' => 'API',
    
    // Actions
    'add' => 'Add',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'create' => 'Create',
    'update' => 'Update',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'back' => 'Back',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    
    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'maintenance' => 'Maintenance',
    'provisioning' => 'Provisioning',
    'decommissioned' => 'Decommissioned',
    'available' => 'Available',
    'assigned' => 'Assigned',
    'reserved' => 'Reserved',
    'blacklisted' => 'Blacklisted',
    
    // Location Management
    'location_management' => 'Location Management',
    'add_location' => 'Add Location',
    'edit_location' => 'Edit Location',
    'location_details' => 'Location Details',
    'location_name' => 'Location Name',
    'location_type' => 'Location Type',
    'parent_location' => 'Parent Location',
    'region' => 'Region',
    'facility' => 'Facility',
    'floor' => 'Floor',
    'address' => 'Address',
    'city' => 'City',
    'state' => 'State/Province',
    'country' => 'Country',
    'postal_code' => 'Postal Code',
    'contact_name' => 'Contact Name',
    'contact_email' => 'Contact Email',
    'contact_phone' => 'Contact Phone',
    'total_racks' => 'Total Racks',
    'power_capacity' => 'Power Capacity',
    'cooling_capacity' => 'Cooling Capacity',
    'notes' => 'Notes',
    
    // Rack Management
    'rack_management' => 'Rack Management',
    'add_rack' => 'Add Rack',
    'edit_rack' => 'Edit Rack',
    'rack_details' => 'Rack Details',
    'rack_name' => 'Rack Name',
    'rack_height' => 'Rack Height (U)',
    'rack_position' => 'Rack Position',
    'manufacturer' => 'Manufacturer',
    'model' => 'Model',
    'serial_number' => 'Serial Number',
    'power_usage' => 'Power Usage',
    'rack_visualization' => 'Rack Visualization',
    'rack_layout' => 'Rack Layout',
    'occupied' => 'Occupied',
    'empty' => 'Empty',
    'rack_units' => 'Rack Units',
    
    // Server Management
    'server_management' => 'Server Management',
    'add_server' => 'Add Server',
    'edit_server' => 'Edit Server',
    'server_details' => 'Server Details',
    'hostname' => 'Hostname',
    'server_status' => 'Server Status',
    'rack_assignment' => 'Rack Assignment',
    'rack_position_start' => 'Start Position (U)',
    'rack_position_end' => 'End Position (U)',
    'asset_tag' => 'Asset Tag',
    'cpu' => 'CPU',
    'ram' => 'RAM (GB)',
    'storage' => 'Storage',
    'network_interfaces' => 'Network Interfaces',
    'power_consumption' => 'Power Consumption (W)',
    'management_ip' => 'Management IP',
    'whmcs_service' => 'WHMCS Service',
    'whmcs_client' => 'WHMCS Client',
    
    // Switch Management
    'switch_management' => 'Switch Management',
    'add_switch' => 'Add Switch',
    'edit_switch' => 'Edit Switch',
    'switch_details' => 'Switch Details',
    'switch_name' => 'Switch Name',
    'port_count' => 'Port Count',
    'management_username' => 'Management Username',
    'management_password' => 'Management Password',
    'vlan_configuration' => 'VLAN Configuration',
    'port_management' => 'Port Management',
    'port_number' => 'Port Number',
    'port_name' => 'Port Name',
    'port_type' => 'Port Type',
    'port_speed' => 'Port Speed',
    'vlan_id' => 'VLAN ID',
    'connected_device' => 'Connected Device',
    'connected_port' => 'Connected Port',
    'ethernet' => 'Ethernet',
    'fiber' => 'Fiber',
    'sfp' => 'SFP',
    'sfp_plus' => 'SFP+',
    
    // IP Address Management
    'ip_management' => 'IP Address Management',
    'subnet_management' => 'Subnet Management',
    'ip_assignments' => 'IP Assignments',
    'add_subnet' => 'Add Subnet',
    'edit_subnet' => 'Edit Subnet',
    'subnet_details' => 'Subnet Details',
    'subnet_name' => 'Subnet Name',
    'network' => 'Network',
    'prefix_length' => 'Prefix Length',
    'ip_version' => 'IP Version',
    'gateway' => 'Gateway',
    'dns_servers' => 'DNS Servers',
    'subnet_description' => 'Subnet Description',
    'ip_address' => 'IP Address',
    'ip_assignment' => 'IP Assignment',
    'assignment_type' => 'Assignment Type',
    'assigned_to' => 'Assigned To',
    'ip_description' => 'IP Description',
    'assign_ip' => 'Assign IP',
    'release_ip' => 'Release IP',
    'bulk_assign' => 'Bulk Assign',
    'ip_utilization' => 'IP Utilization',
    'available_ips' => 'Available IPs',
    'used_ips' => 'Used IPs',
    'total_ips' => 'Total IPs',
    
    // Dashboard
    'dashboard_title' => 'DCIM Dashboard',
    'quick_actions' => 'Quick Actions',
    'recent_activities' => 'Recent Activities',
    'system_alerts' => 'System Alerts',
    'statistics' => 'Statistics',
    'total_locations' => 'Total Locations',
    'total_servers' => 'Total Servers',
    'total_switches' => 'Total Switches',
    'rack_utilization' => 'Rack Utilization',
    
    // Messages
    'success_created' => 'Item created successfully',
    'success_updated' => 'Item updated successfully',
    'success_deleted' => 'Item deleted successfully',
    'error_not_found' => 'Item not found',
    'error_create_failed' => 'Failed to create item',
    'error_update_failed' => 'Failed to update item',
    'error_delete_failed' => 'Failed to delete item',
    'error_has_dependencies' => 'Cannot delete item as it has dependencies',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'no_items_found' => 'No items found',
    'loading' => 'Loading...',
    
    // Validation
    'field_required' => 'This field is required',
    'invalid_ip_address' => 'Invalid IP address format',
    'invalid_network' => 'Invalid network format',
    'duplicate_entry' => 'Duplicate entry',
    'invalid_range' => 'Invalid range',
    
    // API
    'api_documentation' => 'API Documentation',
    'api_endpoints' => 'API Endpoints',
    'api_authentication' => 'API Authentication',
    'api_examples' => 'API Examples',
    'api_key' => 'API Key',
    'api_access_enabled' => 'API Access Enabled',
    'api_access_disabled' => 'API Access Disabled',
    
    // Configuration
    'module_configuration' => 'Module Configuration',
    'enable_ipam' => 'Enable IPAM',
    'enable_rack_visualization' => 'Enable Rack Visualization',
    'default_rack_height' => 'Default Rack Height (U)',
    'enable_api_access' => 'Enable API Access',
    'auto_ip_allocation' => 'Auto IP Allocation',
    
    // Reports
    'reports' => 'Reports',
    'capacity_report' => 'Capacity Report',
    'utilization_report' => 'Utilization Report',
    'inventory_report' => 'Inventory Report',
    'ip_usage_report' => 'IP Usage Report',
    
    // Import/Export
    'import_data' => 'Import Data',
    'export_data' => 'Export Data',
    'csv_format' => 'CSV Format',
    'excel_format' => 'Excel Format',
    'json_format' => 'JSON Format',
    
    // Alerts
    'high_utilization' => 'High Utilization',
    'low_capacity' => 'Low Capacity',
    'maintenance_required' => 'Maintenance Required',
    'no_alerts' => 'No alerts',
    
    // Help
    'help' => 'Help',
    'documentation' => 'Documentation',
    'support' => 'Support',
    'version' => 'Version',
    'about' => 'About'
];
