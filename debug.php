<?php
/**
 * DCIM Debug Script
 * 
 * This script helps debug WHMCS module loading issues
 * Access via: /modules/addons/dcim/debug.php
 */

// Simulate WHMCS environment
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

echo "<h1>DCIM Module Debug</h1>";

// Check PHP version
echo "<h2>PHP Environment</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";
echo "Script Path: " . __FILE__ . "<br>";

// Check file existence
echo "<h2>File Structure Check</h2>";
$files = [
    'dcim.php',
    'lib/AdminController.php',
    'lib/Database.php',
    'lib/Controllers/DashboardController.php',
    'lib/Controllers/LocationController.php',
    'assets/css/dcim.css',
    'assets/js/dcim.js'
];

foreach ($files as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "✓ $file exists<br>";
    } else {
        echo "✗ $file missing<br>";
    }
}

// Check WHMCS integration
echo "<h2>WHMCS Integration</h2>";
$whmcsPath = dirname(dirname(dirname(__DIR__)));
$configPath = $whmcsPath . '/configuration.php';

if (file_exists($configPath)) {
    echo "✓ WHMCS configuration found<br>";
    
    // Try to include WHMCS
    try {
        require_once $configPath;
        require_once $whmcsPath . '/init.php';
        echo "✓ WHMCS initialized<br>";
        
        // Check database connection
        if (class_exists('Illuminate\Database\Capsule\Manager')) {
            echo "✓ Eloquent ORM available<br>";
            
            // Test database connection
            try {
                $result = \Illuminate\Database\Capsule\Manager::select('SELECT 1 as test');
                echo "✓ Database connection working<br>";
            } catch (Exception $e) {
                echo "✗ Database connection failed: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "✗ Eloquent ORM not available<br>";
        }
        
    } catch (Exception $e) {
        echo "✗ WHMCS initialization failed: " . $e->getMessage() . "<br>";
    }
} else {
    echo "✗ WHMCS configuration not found at: $configPath<br>";
}

// Test module functions
echo "<h2>Module Function Test</h2>";

try {
    require_once __DIR__ . '/dcim.php';
    
    if (function_exists('dcim_config')) {
        echo "✓ dcim_config function exists<br>";
        $config = dcim_config();
        echo "Module name: " . $config['name'] . "<br>";
    } else {
        echo "✗ dcim_config function missing<br>";
    }
    
    if (function_exists('dcim_output')) {
        echo "✓ dcim_output function exists<br>";
        
        // Test with minimal vars
        $testVars = ['modulelink' => 'test'];
        $output = dcim_output($testVars);
        
        if (!empty($output)) {
            echo "✓ dcim_output returns content (" . strlen($output) . " chars)<br>";
            echo "<h3>Sample Output:</h3>";
            echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow: auto;'>";
            echo htmlspecialchars(substr($output, 0, 1000));
            if (strlen($output) > 1000) {
                echo "... (truncated)";
            }
            echo "</div>";
        } else {
            echo "✗ dcim_output returns empty content<br>";
        }
    } else {
        echo "✗ dcim_output function missing<br>";
    }
    
} catch (Exception $e) {
    echo "✗ Module loading failed: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}

// Test controller directly
echo "<h2>Direct Controller Test</h2>";

try {
    require_once __DIR__ . '/lib/AdminController.php';
    
    if (class_exists('DCIM\AdminController')) {
        echo "✓ AdminController class exists<br>";
        
        $controller = new DCIM\AdminController(['modulelink' => 'test']);
        echo "✓ AdminController instantiated<br>";
        
        $output = $controller->handleRequest();
        if (!empty($output)) {
            echo "✓ AdminController returns content (" . strlen($output) . " chars)<br>";
        } else {
            echo "✗ AdminController returns empty content<br>";
        }
        
    } else {
        echo "✗ AdminController class not found<br>";
    }
    
} catch (Exception $e) {
    echo "✗ AdminController test failed: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Debug Complete</h2>";
echo "If you see errors above, please share them for further debugging.";
?>
