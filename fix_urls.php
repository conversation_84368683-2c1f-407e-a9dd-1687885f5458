<?php
/**
 * URL Fix Script for DCIM Module
 * 
 * This script automatically fixes hardcoded URLs in all controller files
 * to use proper WHMCS URL generation
 */

echo "DCIM URL Fix Script\n";
echo "===================\n\n";

$controllerFiles = [
    'lib/Controllers/DashboardController.php',
    'lib/Controllers/LocationController.php',
    'lib/Controllers/RackController.php',
    'lib/Controllers/ServerController.php',
    'lib/Controllers/IPAMController.php',
    'lib/Controllers/APIController.php'
];

foreach ($controllerFiles as $file) {
    if (!file_exists($file)) {
        echo "Skipping $file (not found)\n";
        continue;
    }
    
    echo "Processing $file...\n";
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Replace hardcoded URLs with proper URL generation
    $patterns = [
        // Basic module URLs
        '/addonmodules\.php\?module=dcim&action=([^&"\']+)&sub=([^&"\']+)&id=([^&"\']+)/' => '\' . $this->getModuleUrl(\'$1\', [\'sub\' => \'$2\', \'id\' => $3]) . \'',
        '/addonmodules\.php\?module=dcim&action=([^&"\']+)&sub=([^&"\']+)/' => '\' . $this->getModuleUrl(\'$1\', [\'sub\' => \'$2\']) . \'',
        '/addonmodules\.php\?module=dcim&action=([^&"\']+)&([^=]+)=([^&"\']+)/' => '\' . $this->getModuleUrl(\'$1\', [\'$2\' => \'$3\']) . \'',
        '/addonmodules\.php\?module=dcim&action=([^&"\']+)/' => '\' . $this->getModuleUrl(\'$1\') . \'',
        '/addonmodules\.php\?module=dcim/' => '\' . $this->getModuleUrl() . \'',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // Fix header redirects specifically
    $content = preg_replace(
        '/header\(\'Location: addonmodules\.php\?module=dcim([^\']*)\'\);/',
        'header(\'Location: \' . $this->getModuleUrl() . \'$1\');',
        $content
    );
    
    if ($content !== $originalContent) {
        file_put_contents($file, $content);
        echo "✓ Updated $file\n";
    } else {
        echo "- No changes needed for $file\n";
    }
}

echo "\nURL fix completed!\n";
echo "Please test the module now.\n";
?>
