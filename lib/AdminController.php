<?php
/**
 * DCIM Admin Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM;

class AdminController {
    
    private $vars;
    private $action;
    
    public function __construct($vars) {
        $this->vars = $vars;
        $this->action = $_GET['action'] ?? 'dashboard';
    }
    
    /**
     * Handle incoming requests and route to appropriate controller
     */
    public function handleRequest() {
        // Include CSS and JS assets
        $output = $this->includeAssets();
        
        // Route to appropriate action
        switch ($this->action) {
            case 'dashboard':
                $output .= $this->showDashboard();
                break;
            case 'locations':
                $output .= $this->handleLocations();
                break;
            case 'racks':
                $output .= $this->handleRacks();
                break;
            case 'servers':
                $output .= $this->handleServers();
                break;
            case 'switches':
                $output .= $this->handleSwitches();
                break;
            case 'ipam':
                $output .= $this->handleIPAM();
                break;
            case 'api':
                $output .= $this->handleAPI();
                break;
            default:
                $output .= $this->showDashboard();
        }
        
        return $output;
    }
    
    /**
     * Include CSS and JS assets
     */
    private function includeAssets() {
        $output = '<link rel="stylesheet" type="text/css" href="modules/addons/dcim/assets/css/dcim.css" />';
        $output .= '<script type="text/javascript" src="modules/addons/dcim/assets/js/dcim.js"></script>';
        return $output;
    }
    
    /**
     * Show main dashboard
     */
    private function showDashboard() {
        require_once __DIR__ . '/Controllers/DashboardController.php';
        $controller = new Controllers\DashboardController($this->vars);
        return $controller->index();
    }
    
    /**
     * Handle location management
     */
    private function handleLocations() {
        require_once __DIR__ . '/Controllers/LocationController.php';
        $controller = new Controllers\LocationController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle rack management
     */
    private function handleRacks() {
        require_once __DIR__ . '/Controllers/RackController.php';
        $controller = new Controllers\RackController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            case 'visual':
                return $controller->visualLayout();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle server management
     */
    private function handleServers() {
        require_once __DIR__ . '/Controllers/ServerController.php';
        $controller = new Controllers\ServerController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            case 'assign':
                return $controller->assignToRack();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle switch management
     */
    private function handleSwitches() {
        require_once __DIR__ . '/Controllers/SwitchController.php';
        $controller = new Controllers\SwitchController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            case 'ports':
                return $controller->managePorts();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle IPAM (IP Address Management)
     */
    private function handleIPAM() {
        require_once __DIR__ . '/Controllers/IPAMController.php';
        $controller = new Controllers\IPAMController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'subnets':
                return $controller->manageSubnets();
            case 'assignments':
                return $controller->manageAssignments();
            case 'create_subnet':
                return $controller->createSubnet();
            case 'edit_subnet':
                return $controller->editSubnet();
            case 'assign_ip':
                return $controller->assignIP();
            case 'bulk_assign':
                return $controller->bulkAssign();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle API management
     */
    private function handleAPI() {
        require_once __DIR__ . '/Controllers/APIController.php';
        $controller = new Controllers\APIController($this->vars);
        return $controller->index();
    }
}
