<?php
/**
 * DCIM Admin Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM;

require_once __DIR__ . '/UrlHelper.php';

class AdminController {

    private $vars;
    private $action;
    private $moduleLink;
    
    public function __construct($vars) {
        $this->vars = $vars;
        $this->action = $_GET['action'] ?? 'dashboard';

        // Store the module link for URL generation
        $this->moduleLink = $vars['modulelink'] ?? 'addonmodules.php?module=dcim';

        // Set the module link in the URL helper
        UrlHelper::setModuleLink($this->moduleLink);
    }
    
    /**
     * Handle incoming requests and route to appropriate controller
     */
    public function handleRequest() {
        try {
            // Include CSS and JS assets
            $output = $this->includeAssets();

            // Debug info
            $output .= '<!-- DCIM Debug: Action = ' . $this->action . ' -->';

            // Route to appropriate action
            switch ($this->action) {
                case 'dashboard':
                    $output .= $this->showDashboard();
                    break;
                case 'locations':
                    $output .= $this->handleLocations();
                    break;
                case 'racks':
                    $output .= $this->handleRacks();
                    break;
                case 'servers':
                    $output .= $this->handleServers();
                    break;
                case 'switches':
                    $output .= $this->handleSwitches();
                    break;
                case 'ipam':
                    $output .= $this->handleIPAM();
                    break;
                case 'api':
                    $output .= $this->handleAPI();
                    break;
                default:
                    $output .= $this->showDashboard();
            }

            return $output;

        } catch (Exception $e) {
            return '<div class="alert alert-danger">AdminController Error: ' . htmlspecialchars($e->getMessage()) . '<br><pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre></div>';
        }
    }
    
    /**
     * Include CSS and JS assets
     */
    private function includeAssets() {
        $output = '<link rel="stylesheet" type="text/css" href="' . UrlHelper::getAssetPath('css/dcim.css') . '" />';
        $output .= '<script type="text/javascript" src="' . UrlHelper::getAssetPath('js/dcim.js') . '"></script>';
        return $output;
    }
    
    /**
     * Show main dashboard
     */
    private function showDashboard() {
        try {
            $dashboardPath = __DIR__ . '/Controllers/DashboardController.php';
            if (!file_exists($dashboardPath)) {
                return '<div class="alert alert-danger">Error: DashboardController.php not found at: ' . $dashboardPath . '</div>';
            }

            require_once $dashboardPath;

            if (!class_exists('DCIM\Controllers\DashboardController')) {
                return '<div class="alert alert-danger">Error: DCIM\Controllers\DashboardController class not found</div>';
            }

            $controller = new Controllers\DashboardController($this->vars);
            $result = $controller->index();

            if (empty($result)) {
                return '<div class="alert alert-warning">Debug: DashboardController returned empty result</div>';
            }

            return $result;

        } catch (Exception $e) {
            return '<div class="alert alert-danger">Dashboard Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    
    /**
     * Handle location management
     */
    private function handleLocations() {
        require_once __DIR__ . '/Controllers/LocationController.php';
        $controller = new Controllers\LocationController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle rack management
     */
    private function handleRacks() {
        require_once __DIR__ . '/Controllers/RackController.php';
        $controller = new Controllers\RackController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            case 'visual':
                return $controller->visualLayout();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle server management
     */
    private function handleServers() {
        require_once __DIR__ . '/Controllers/ServerController.php';
        $controller = new Controllers\ServerController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            case 'assign':
                return $controller->assignToRack();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle switch management
     */
    private function handleSwitches() {
        require_once __DIR__ . '/Controllers/SwitchController.php';
        $controller = new Controllers\SwitchController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'create':
                return $controller->create();
            case 'edit':
                return $controller->edit();
            case 'delete':
                return $controller->delete();
            case 'view':
                return $controller->view();
            case 'ports':
                return $controller->managePorts();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle IPAM (IP Address Management)
     */
    private function handleIPAM() {
        require_once __DIR__ . '/Controllers/IPAMController.php';
        $controller = new Controllers\IPAMController($this->vars);
        
        $subAction = $_GET['sub'] ?? 'index';
        
        switch ($subAction) {
            case 'subnets':
                return $controller->manageSubnets();
            case 'assignments':
                return $controller->manageAssignments();
            case 'create_subnet':
                return $controller->createSubnet();
            case 'edit_subnet':
                return $controller->editSubnet();
            case 'assign_ip':
                return $controller->assignIP();
            case 'bulk_assign':
                return $controller->bulkAssign();
            default:
                return $controller->index();
        }
    }
    
    /**
     * Handle API management
     */
    private function handleAPI() {
        require_once __DIR__ . '/Controllers/APIController.php';
        $controller = new Controllers\APIController($this->vars);
        return $controller->index();
    }

    /**
     * Generate proper module URL
     */
    public function getModuleUrl($action = '', $params = []) {
        $url = $this->moduleLink;

        if ($action) {
            $url .= '&action=' . urlencode($action);
        }

        foreach ($params as $key => $value) {
            $url .= '&' . urlencode($key) . '=' . urlencode($value);
        }

        return $url;
    }
}
