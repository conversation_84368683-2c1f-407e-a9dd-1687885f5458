<?php
/**
 * DCIM Database Management Class
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM;

use Illuminate\Database\Capsule\Manager as Capsule;

class Database {
    
    /**
     * Create all required database tables
     */
    public function createTables() {
        try {
            // Disable foreign key checks temporarily
            Capsule::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Create locations table first (no dependencies)
            if (!Capsule::schema()->hasTable('dcim_locations')) {
                Capsule::schema()->create('dcim_locations', function ($table) {
                    $table->increments('id');
                    $table->string('name');
                    $table->string('type')->default('facility'); // region, facility, floor
                    $table->integer('parent_id')->unsigned()->nullable();
                    $table->text('address')->nullable();
                    $table->string('city')->nullable();
                    $table->string('state')->nullable();
                    $table->string('country')->nullable();
                    $table->string('postal_code')->nullable();
                    $table->string('contact_name')->nullable();
                    $table->string('contact_email')->nullable();
                    $table->string('contact_phone')->nullable();
                    $table->integer('total_racks')->default(0);
                    $table->decimal('total_power_capacity', 10, 2)->nullable();
                    $table->decimal('total_cooling_capacity', 10, 2)->nullable();
                    $table->text('notes')->nullable();
                    $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                    $table->timestamps();

                    $table->index(['parent_id']);
                    $table->index(['type']);
                    $table->index(['status']);
                });
            }
            
            // Create racks table (depends on locations)
            if (!Capsule::schema()->hasTable('dcim_racks')) {
                Capsule::schema()->create('dcim_racks', function ($table) {
                    $table->increments('id');
                    $table->string('name');
                    $table->integer('location_id')->unsigned();
                    $table->integer('height_units')->default(42);
                    $table->decimal('power_capacity', 10, 2)->nullable();
                    $table->decimal('power_usage', 10, 2)->default(0);
                    $table->decimal('cooling_capacity', 10, 2)->nullable();
                    $table->string('manufacturer')->nullable();
                    $table->string('model')->nullable();
                    $table->string('serial_number')->nullable();
                    $table->integer('position_x')->nullable();
                    $table->integer('position_y')->nullable();
                    $table->text('notes')->nullable();
                    $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                    $table->timestamps();

                    $table->index(['location_id']);
                    $table->index(['status']);
                });
            }
            
            // Create servers table (depends on racks)
            if (!Capsule::schema()->hasTable('dcim_servers')) {
                Capsule::schema()->create('dcim_servers', function ($table) {
                    $table->increments('id');
                    $table->string('hostname');
                    $table->integer('rack_id')->unsigned();
                    $table->integer('rack_position_start');
                    $table->integer('rack_position_end');
                    $table->string('manufacturer')->nullable();
                    $table->string('model')->nullable();
                    $table->string('serial_number')->nullable();
                    $table->string('asset_tag')->nullable();
                    $table->string('cpu')->nullable();
                    $table->integer('ram_gb')->nullable();
                    $table->string('storage')->nullable();
                    $table->string('network_interfaces')->nullable();
                    $table->decimal('power_consumption', 8, 2)->nullable();
                    $table->string('management_ip')->nullable();
                    $table->integer('whmcs_service_id')->unsigned()->nullable();
                    $table->integer('whmcs_client_id')->unsigned()->nullable();
                    $table->enum('status', ['provisioning', 'active', 'maintenance', 'decommissioned'])->default('provisioning');
                    $table->text('notes')->nullable();
                    $table->timestamps();

                    $table->index(['rack_id']);
                    $table->index(['whmcs_service_id']);
                    $table->index(['whmcs_client_id']);
                    $table->index(['status']);
                    $table->unique(['hostname']);
                });
            }

            // Create switches table (depends on locations and racks)
            if (!Capsule::schema()->hasTable('dcim_switches')) {
                Capsule::schema()->create('dcim_switches', function ($table) {
                    $table->increments('id');
                    $table->string('name');
                    $table->integer('location_id')->unsigned();
                    $table->integer('rack_id')->unsigned()->nullable();
                    $table->integer('rack_position_start')->nullable();
                    $table->integer('rack_position_end')->nullable();
                    $table->string('manufacturer')->nullable();
                    $table->string('model')->nullable();
                    $table->string('serial_number')->nullable();
                    $table->integer('port_count')->default(24);
                    $table->string('management_ip')->nullable();
                    $table->string('management_username')->nullable();
                    $table->string('management_password')->nullable();
                    $table->text('vlan_configuration')->nullable();
                    $table->decimal('power_consumption', 8, 2)->nullable();
                    $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
                    $table->text('notes')->nullable();
                    $table->timestamps();

                    $table->index(['location_id']);
                    $table->index(['rack_id']);
                    $table->index(['status']);
                });
            }

            // Create switch ports table (depends on switches)
            if (!Capsule::schema()->hasTable('dcim_switch_ports')) {
                Capsule::schema()->create('dcim_switch_ports', function ($table) {
                    $table->increments('id');
                    $table->integer('switch_id')->unsigned();
                    $table->integer('port_number');
                    $table->string('port_name')->nullable();
                    $table->enum('port_type', ['ethernet', 'fiber', 'sfp', 'sfp+'])->default('ethernet');
                    $table->enum('port_speed', ['10M', '100M', '1G', '10G', '25G', '40G', '100G'])->default('1G');
                    $table->integer('vlan_id')->nullable();
                    $table->string('connected_device')->nullable();
                    $table->string('connected_port')->nullable();
                    $table->enum('status', ['active', 'inactive', 'disabled'])->default('active');
                    $table->text('notes')->nullable();
                    $table->timestamps();

                    $table->index(['switch_id']);
                    $table->index(['vlan_id']);
                    $table->index(['status']);
                    $table->unique(['switch_id', 'port_number']);
                });
            }

            // Create subnets table (depends on locations)
            if (!Capsule::schema()->hasTable('dcim_subnets')) {
                Capsule::schema()->create('dcim_subnets', function ($table) {
                    $table->increments('id');
                    $table->string('name');
                    $table->string('network'); // e.g., ***********/24
                    $table->integer('prefix_length');
                    $table->enum('ip_version', ['4', '6'])->default('4');
                    $table->string('gateway')->nullable();
                    $table->string('dns_servers')->nullable();
                    $table->integer('vlan_id')->nullable();
                    $table->integer('location_id')->unsigned()->nullable();
                    $table->text('description')->nullable();
                    $table->enum('status', ['active', 'inactive', 'reserved'])->default('active');
                    $table->timestamps();

                    $table->index(['location_id']);
                    $table->index(['vlan_id']);
                    $table->index(['status']);
                    $table->index(['ip_version']);
                });
            }

            // Create IP assignments table (depends on subnets)
            if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
                Capsule::schema()->create('dcim_ip_assignments', function ($table) {
                    $table->increments('id');
                    $table->integer('subnet_id')->unsigned();
                    $table->string('ip_address');
                    $table->string('hostname')->nullable();
                    $table->enum('assignment_type', ['server', 'switch', 'client', 'service', 'reserved', 'available'])->default('available');
                    $table->integer('assigned_to_id')->unsigned()->nullable(); // server_id, switch_id, client_id, service_id
                    $table->integer('whmcs_client_id')->unsigned()->nullable();
                    $table->integer('whmcs_service_id')->unsigned()->nullable();
                    $table->text('description')->nullable();
                    $table->enum('status', ['active', 'inactive', 'reserved', 'blacklisted', 'available'])->default('available');
                    $table->timestamps();

                    $table->index(['subnet_id']);
                    $table->index(['assignment_type']);
                    $table->index(['assigned_to_id']);
                    $table->index(['whmcs_client_id']);
                    $table->index(['whmcs_service_id']);
                    $table->index(['status']);
                    $table->unique(['ip_address']);
                });
            }

            // Now add foreign key constraints after all tables are created
            $this->addForeignKeyConstraints();

            // Re-enable foreign key checks
            Capsule::statement('SET FOREIGN_KEY_CHECKS=1;');

            return ['success' => true];
            
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Add foreign key constraints after all tables are created
     */
    private function addForeignKeyConstraints() {
        try {
            // Add foreign key for racks -> locations
            if (!$this->foreignKeyExists('dcim_racks', 'dcim_racks_location_id_foreign')) {
                Capsule::schema()->table('dcim_racks', function ($table) {
                    $table->foreign('location_id', 'dcim_racks_location_id_foreign')
                          ->references('id')->on('dcim_locations')->onDelete('cascade');
                });
            }

            // Add foreign key for servers -> racks
            if (!$this->foreignKeyExists('dcim_servers', 'dcim_servers_rack_id_foreign')) {
                Capsule::schema()->table('dcim_servers', function ($table) {
                    $table->foreign('rack_id', 'dcim_servers_rack_id_foreign')
                          ->references('id')->on('dcim_racks')->onDelete('cascade');
                });
            }

            // Add foreign key for switches -> locations
            if (!$this->foreignKeyExists('dcim_switches', 'dcim_switches_location_id_foreign')) {
                Capsule::schema()->table('dcim_switches', function ($table) {
                    $table->foreign('location_id', 'dcim_switches_location_id_foreign')
                          ->references('id')->on('dcim_locations')->onDelete('cascade');
                });
            }

            // Add foreign key for switches -> racks (nullable)
            if (!$this->foreignKeyExists('dcim_switches', 'dcim_switches_rack_id_foreign')) {
                Capsule::schema()->table('dcim_switches', function ($table) {
                    $table->foreign('rack_id', 'dcim_switches_rack_id_foreign')
                          ->references('id')->on('dcim_racks')->onDelete('set null');
                });
            }

            // Add foreign key for switch_ports -> switches
            if (!$this->foreignKeyExists('dcim_switch_ports', 'dcim_switch_ports_switch_id_foreign')) {
                Capsule::schema()->table('dcim_switch_ports', function ($table) {
                    $table->foreign('switch_id', 'dcim_switch_ports_switch_id_foreign')
                          ->references('id')->on('dcim_switches')->onDelete('cascade');
                });
            }

            // Add foreign key for subnets -> locations (nullable)
            if (!$this->foreignKeyExists('dcim_subnets', 'dcim_subnets_location_id_foreign')) {
                Capsule::schema()->table('dcim_subnets', function ($table) {
                    $table->foreign('location_id', 'dcim_subnets_location_id_foreign')
                          ->references('id')->on('dcim_locations')->onDelete('set null');
                });
            }

            // Add foreign key for ip_assignments -> subnets
            if (!$this->foreignKeyExists('dcim_ip_assignments', 'dcim_ip_assignments_subnet_id_foreign')) {
                Capsule::schema()->table('dcim_ip_assignments', function ($table) {
                    $table->foreign('subnet_id', 'dcim_ip_assignments_subnet_id_foreign')
                          ->references('id')->on('dcim_subnets')->onDelete('cascade');
                });
            }

        } catch (\Exception $e) {
            // Log error but don't fail the entire installation
            error_log("DCIM: Error adding foreign key constraints: " . $e->getMessage());
        }
    }

    /**
     * Check if foreign key constraint exists
     */
    private function foreignKeyExists($table, $constraintName) {
        try {
            $result = Capsule::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.TABLE_CONSTRAINTS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND CONSTRAINT_NAME = ?
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ", [$table, $constraintName]);

            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Drop all DCIM tables (for uninstallation)
     */
    public function dropTables() {
        try {
            $tables = [
                'dcim_ip_assignments',
                'dcim_subnets', 
                'dcim_switch_ports',
                'dcim_switches',
                'dcim_servers',
                'dcim_racks',
                'dcim_locations'
            ];
            
            foreach ($tables as $table) {
                if (Capsule::schema()->hasTable($table)) {
                    Capsule::schema()->drop($table);
                }
            }
            
            return ['success' => true];
            
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
