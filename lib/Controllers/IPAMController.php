<?php
/**
 * DCIM IPAM Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM\Controllers;

use Illuminate\Database\Capsule\Manager as Capsule;

class IPAMController {
    
    private $vars;
    
    public function __construct($vars) {
        $this->vars = $vars;
    }
    
    /**
     * Display IPAM dashboard
     */
    public function index() {
        $subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select([
                'dcim_subnets.*',
                'dcim_locations.name as location_name'
            ])
            ->orderBy('dcim_subnets.ip_version')
            ->orderBy('dcim_subnets.network')
            ->get();
        
        $output = '<div class="dcim-ipam">';
        $output .= '<div class="d-flex justify-content-between align-items-center mb-3">';
        $output .= '<h2>IP Address Management</h2>';
        $output .= '<div>';
        $output .= '<a href="addonmodules.php?module=dcim&action=ipam&sub=assignments" class="btn btn-info">View Assignments</a> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=ipam&sub=create_subnet" class="btn btn-primary">Create Subnet</a>';
        $output .= '</div>';
        $output .= '</div>';
        
        // IPAM Statistics
        $totalSubnets = $subnets->count();
        $totalIPs = Capsule::table('dcim_ip_assignments')->count();
        $assignedIPs = Capsule::table('dcim_ip_assignments')->where('status', 'active')->count();
        $availableIPs = Capsule::table('dcim_ip_assignments')->where('status', 'available')->count();
        
        $output .= '<div class="row mb-4">';
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-primary text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $totalSubnets . '</h4>';
        $output .= '<p>Total Subnets</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-success text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $assignedIPs . '</h4>';
        $output .= '<p>Assigned IPs</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-info text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $availableIPs . '</h4>';
        $output .= '<p>Available IPs</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-warning text-white">';
        $output .= '<div class="card-body">';
        $utilization = $totalIPs > 0 ? round(($assignedIPs / $totalIPs) * 100, 1) : 0;
        $output .= '<h4>' . $utilization . '%</h4>';
        $output .= '<p>Utilization</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Subnets List
        if ($subnets->count() > 0) {
            foreach ($subnets as $subnet) {
                $assignments = Capsule::table('dcim_ip_assignments')
                    ->where('subnet_id', $subnet->id)
                    ->get();
                
                $totalIPs = $assignments->count();
                $assignedIPs = $assignments->where('status', 'active')->count();
                $availableIPs = $assignments->where('status', 'available')->count();
                $reservedIPs = $assignments->where('status', 'reserved')->count();
                
                $utilization = $totalIPs > 0 ? round(($assignedIPs / $totalIPs) * 100, 1) : 0;
                $utilizationClass = $utilization > 80 ? 'danger' : ($utilization > 60 ? 'warning' : 'success');
                
                $output .= '<div class="ip-subnet card mb-3" data-subnet-id="' . $subnet->id . '" data-total-ips="' . $totalIPs . '" data-used-ips="' . $assignedIPs . '">';
                $output .= '<div class="ip-subnet-header card-header">';
                $output .= '<div class="d-flex justify-content-between align-items-center">';
                $output .= '<div>';
                $output .= '<h5 class="mb-0">' . htmlspecialchars($subnet->name) . '</h5>';
                $output .= '<small class="text-muted">' . htmlspecialchars($subnet->network) . '/' . $subnet->prefix_length . ' (IPv' . $subnet->ip_version . ')</small>';
                if ($subnet->location_name) {
                    $output .= ' - <small class="text-muted">' . htmlspecialchars($subnet->location_name) . '</small>';
                }
                $output .= '</div>';
                $output .= '<div>';
                $output .= '<span class="badge badge-' . $this->getStatusBadgeClass($subnet->status) . '">' . ucfirst($subnet->status) . '</span>';
                $output .= '</div>';
                $output .= '</div>';
                
                // Utilization bar
                $output .= '<div class="progress mt-2" style="height: 8px;">';
                $output .= '<div class="progress-bar bg-success" style="width: ' . ($totalIPs > 0 ? ($assignedIPs / $totalIPs) * 100 : 0) . '%" title="Assigned: ' . $assignedIPs . '"></div>';
                $output .= '<div class="progress-bar bg-warning" style="width: ' . ($totalIPs > 0 ? ($reservedIPs / $totalIPs) * 100 : 0) . '%" title="Reserved: ' . $reservedIPs . '"></div>';
                $output .= '</div>';
                $output .= '</div>';
                
                $output .= '<div class="ip-subnet-body card-body">';
                $output .= '<div class="row">';
                $output .= '<div class="col-md-8">';
                
                // IP Address Grid (show first 50 IPs)
                $output .= '<div class="ip-addresses">';
                $displayCount = 0;
                foreach ($assignments->take(50) as $ip) {
                    $statusClass = $this->getIPStatusClass($ip->status);
                    $title = $ip->hostname ? htmlspecialchars($ip->hostname) : htmlspecialchars($ip->ip_address);
                    if ($ip->description) {
                        $title .= ' - ' . htmlspecialchars($ip->description);
                    }
                    
                    $output .= '<span class="ip-address ' . $statusClass . '" title="' . $title . '" data-assignment-id="' . $ip->id . '">';
                    $output .= htmlspecialchars($ip->ip_address);
                    $output .= '</span>';
                    $displayCount++;
                }
                
                if ($totalIPs > 50) {
                    $output .= '<span class="text-muted">... and ' . ($totalIPs - 50) . ' more</span>';
                }
                
                $output .= '</div>';
                $output .= '</div>';
                
                $output .= '<div class="col-md-4">';
                $output .= '<div class="subnet-stats">';
                $output .= '<p><strong>Total IPs:</strong> ' . $totalIPs . '</p>';
                $output .= '<p><strong>Assigned:</strong> ' . $assignedIPs . '</p>';
                $output .= '<p><strong>Available:</strong> ' . $availableIPs . '</p>';
                $output .= '<p><strong>Reserved:</strong> ' . $reservedIPs . '</p>';
                $output .= '<p><strong>Utilization:</strong> ' . $utilization . '%</p>';
                
                if ($subnet->gateway) {
                    $output .= '<p><strong>Gateway:</strong> ' . htmlspecialchars($subnet->gateway) . '</p>';
                }
                
                $output .= '<div class="mt-3">';
                $output .= '<a href="addonmodules.php?module=dcim&action=ipam&sub=assign_ip&subnet_id=' . $subnet->id . '" class="btn btn-sm btn-success">Assign IP</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=ipam&sub=edit_subnet&id=' . $subnet->id . '" class="btn btn-sm btn-warning">Edit</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=ipam&sub=delete_subnet&id=' . $subnet->id . '" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure?\')">Delete</a>';
                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
                $output .= '</div>';
            }
        } else {
            $output .= '<div class="alert alert-info">No subnets found. <a href="addonmodules.php?module=dcim&action=ipam&sub=create_subnet">Create your first subnet</a>.</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Show create subnet form
     */
    public function createSubnet() {
        if ($_POST) {
            return $this->storeSubnet();
        }
        
        $locations = Capsule::table('dcim_locations')
            ->orderBy('name')
            ->get();
        
        $output = '<div class="dcim-subnet-create">';
        $output .= '<h2>Create New Subnet</h2>';
        
        $output .= '<form method="post">';
        $output .= '<div class="row">';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="name">Subnet Name *</label>';
        $output .= '<input type="text" class="form-control" id="name" name="name" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="ip_version">IP Version</label>';
        $output .= '<select class="form-control" id="ip_version" name="ip_version">';
        $output .= '<option value="4" selected>IPv4</option>';
        $output .= '<option value="6">IPv6</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-8">';
        $output .= '<div class="form-group">';
        $output .= '<label for="network">Network *</label>';
        $output .= '<input type="text" class="form-control" id="network" name="network" placeholder="***********" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="prefix_length">Prefix Length *</label>';
        $output .= '<input type="number" class="form-control" id="prefix_length" name="prefix_length" min="1" max="32" value="24" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="gateway">Gateway</label>';
        $output .= '<input type="text" class="form-control" id="gateway" name="gateway" placeholder="192.168.1.1">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="dns_servers">DNS Servers</label>';
        $output .= '<input type="text" class="form-control" id="dns_servers" name="dns_servers" placeholder="8.8.8.8,8.8.4.4">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="vlan_id">VLAN ID</label>';
        $output .= '<input type="number" class="form-control" id="vlan_id" name="vlan_id" min="1" max="4094">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="location_id">Location</label>';
        $output .= '<select class="form-control" id="location_id" name="location_id">';
        $output .= '<option value="">Select Location</option>';
        foreach ($locations as $location) {
            $output .= '<option value="' . $location->id . '">' . htmlspecialchars($location->name) . '</option>';
        }
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-12">';
        $output .= '<div class="form-group">';
        $output .= '<label for="description">Description</label>';
        $output .= '<textarea class="form-control" id="description" name="description" rows="3"></textarea>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="status">Status</label>';
        $output .= '<select class="form-control" id="status" name="status">';
        $output .= '<option value="active" selected>Active</option>';
        $output .= '<option value="inactive">Inactive</option>';
        $output .= '<option value="reserved">Reserved</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        $output .= '<div class="form-group">';
        $output .= '<button type="submit" class="btn btn-primary">Create Subnet</button> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=ipam" class="btn btn-secondary">Cancel</a>';
        $output .= '</div>';
        
        $output .= '</form>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Store new subnet and generate IP assignments
     */
    private function storeSubnet() {
        try {
            $network = $_POST['network'];
            $prefixLength = $_POST['prefix_length'];
            $ipVersion = $_POST['ip_version'];
            
            // Validate network format
            if ($ipVersion == '4') {
                if (!filter_var($network, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    throw new \Exception('Invalid IPv4 network address');
                }
            } else {
                if (!filter_var($network, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                    throw new \Exception('Invalid IPv6 network address');
                }
            }
            
            $data = [
                'name' => $_POST['name'],
                'network' => $network,
                'prefix_length' => $prefixLength,
                'ip_version' => $ipVersion,
                'gateway' => $_POST['gateway'] ?? null,
                'dns_servers' => $_POST['dns_servers'] ?? null,
                'vlan_id' => !empty($_POST['vlan_id']) ? $_POST['vlan_id'] : null,
                'location_id' => !empty($_POST['location_id']) ? $_POST['location_id'] : null,
                'description' => $_POST['description'] ?? null,
                'status' => $_POST['status'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $subnetId = Capsule::table('dcim_subnets')->insertGetId($data);
            
            // Generate IP assignments for IPv4 subnets
            if ($ipVersion == '4' && $prefixLength >= 24) {
                $this->generateIPv4Assignments($subnetId, $network, $prefixLength);
            }
            
            header('Location: addonmodules.php?module=dcim&action=ipam&success=subnet_created');
            exit;
            
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">Error creating subnet: ' . $e->getMessage() . '</div>' . $this->createSubnet();
        }
    }
    
    /**
     * Generate IP assignments for IPv4 subnet
     */
    private function generateIPv4Assignments($subnetId, $network, $prefixLength) {
        $networkLong = ip2long($network);
        $hostBits = 32 - $prefixLength;
        $numHosts = pow(2, $hostBits) - 2; // Exclude network and broadcast
        
        // Limit to reasonable subnet sizes
        if ($numHosts > 1000) {
            return; // Skip auto-generation for large subnets
        }
        
        $assignments = [];
        for ($i = 1; $i <= $numHosts; $i++) {
            $ipLong = $networkLong + $i;
            $ipAddress = long2ip($ipLong);
            
            $assignments[] = [
                'subnet_id' => $subnetId,
                'ip_address' => $ipAddress,
                'assignment_type' => 'available',
                'status' => 'available',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        
        // Insert in batches
        $chunks = array_chunk($assignments, 100);
        foreach ($chunks as $chunk) {
            Capsule::table('dcim_ip_assignments')->insert($chunk);
        }
    }
    
    /**
     * Get status badge class
     */
    private function getStatusBadgeClass($status) {
        switch ($status) {
            case 'active': return 'success';
            case 'inactive': return 'secondary';
            case 'reserved': return 'warning';
            default: return 'secondary';
        }
    }
    
    /**
     * Get IP status CSS class
     */
    private function getIPStatusClass($status) {
        switch ($status) {
            case 'available': return 'available';
            case 'active': return 'assigned';
            case 'reserved': return 'reserved';
            case 'blacklisted': return 'blacklisted';
            default: return 'available';
        }
    }
}
