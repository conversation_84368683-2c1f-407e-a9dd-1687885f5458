<?php
/**
 * DCIM Server Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM\Controllers;

use Illuminate\Database\Capsule\Manager as Capsule;

class ServerController {
    
    private $vars;
    
    public function __construct($vars) {
        $this->vars = $vars;
    }
    
    /**
     * Display servers list
     */
    public function index() {
        $servers = Capsule::table('dcim_servers')
            ->join('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->leftJoin('tblhosting', 'dcim_servers.whmcs_service_id', '=', 'tblhosting.id')
            ->leftJoin('tblclients', 'dcim_servers.whmcs_client_id', '=', 'tblclients.id')
            ->select([
                'dcim_servers.*',
                'dcim_racks.name as rack_name',
                'dcim_locations.name as location_name',
                'tblhosting.domain as service_domain',
                'tblclients.firstname',
                'tblclients.lastname'
            ])
            ->orderBy('dcim_locations.name')
            ->orderBy('dcim_racks.name')
            ->orderBy('dcim_servers.hostname')
            ->get();
        
        $output = '<div class="dcim-servers">';
        $output .= '<div class="d-flex justify-content-between align-items-center mb-3">';
        $output .= '<h2>Server Management</h2>';
        $output .= '<a href="addonmodules.php?module=dcim&action=servers&sub=create" class="btn btn-primary">Add Server</a>';
        $output .= '</div>';
        
        // Status filter
        $output .= '<div class="mb-3">';
        $output .= '<div class="btn-group" role="group">';
        $output .= '<button type="button" class="btn btn-outline-secondary active" onclick="filterServers(\'all\')">All</button>';
        $output .= '<button type="button" class="btn btn-outline-success" onclick="filterServers(\'active\')">Active</button>';
        $output .= '<button type="button" class="btn btn-outline-info" onclick="filterServers(\'provisioning\')">Provisioning</button>';
        $output .= '<button type="button" class="btn btn-outline-warning" onclick="filterServers(\'maintenance\')">Maintenance</button>';
        $output .= '<button type="button" class="btn btn-outline-danger" onclick="filterServers(\'decommissioned\')">Decommissioned</button>';
        $output .= '</div>';
        $output .= '</div>';
        
        if ($servers->count() > 0) {
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-striped" id="servers-table">';
            $output .= '<thead>';
            $output .= '<tr>';
            $output .= '<th>Hostname</th>';
            $output .= '<th>Location</th>';
            $output .= '<th>Rack</th>';
            $output .= '<th>Position</th>';
            $output .= '<th>Hardware</th>';
            $output .= '<th>WHMCS Client</th>';
            $output .= '<th>Status</th>';
            $output .= '<th>Actions</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody>';
            
            foreach ($servers as $server) {
                $clientName = $server->firstname && $server->lastname ? 
                    $server->firstname . ' ' . $server->lastname : 
                    'N/A';
                
                $hardware = [];
                if ($server->cpu) $hardware[] = $server->cpu;
                if ($server->ram_gb) $hardware[] = $server->ram_gb . 'GB RAM';
                if ($server->storage) $hardware[] = $server->storage;
                
                $output .= '<tr data-status="' . $server->status . '">';
                $output .= '<td>';
                $output .= '<strong>' . htmlspecialchars($server->hostname) . '</strong>';
                if ($server->management_ip) {
                    $output .= '<br><small class="text-muted">IP: ' . htmlspecialchars($server->management_ip) . '</small>';
                }
                $output .= '</td>';
                $output .= '<td>' . htmlspecialchars($server->location_name) . '</td>';
                $output .= '<td>' . htmlspecialchars($server->rack_name) . '</td>';
                $output .= '<td>U' . $server->rack_position_start;
                if ($server->rack_position_end != $server->rack_position_start) {
                    $output .= '-U' . $server->rack_position_end;
                }
                $output .= '</td>';
                $output .= '<td>' . implode('<br>', $hardware) . '</td>';
                $output .= '<td>' . htmlspecialchars($clientName) . '</td>';
                $output .= '<td><span class="badge badge-' . $this->getStatusBadgeClass($server->status) . '">' . ucfirst($server->status) . '</span></td>';
                $output .= '<td>';
                $output .= '<a href="addonmodules.php?module=dcim&action=servers&sub=view&id=' . $server->id . '" class="btn btn-sm btn-info">View</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=servers&sub=edit&id=' . $server->id . '" class="btn btn-sm btn-warning">Edit</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=servers&sub=delete&id=' . $server->id . '" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure?\')">Delete</a>';
                $output .= '</td>';
                $output .= '</tr>';
            }
            
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
        } else {
            $output .= '<div class="alert alert-info">No servers found. <a href="addonmodules.php?module=dcim&action=servers&sub=create">Add your first server</a>.</div>';
        }
        
        $output .= '</div>';
        
        // Add JavaScript for filtering
        $output .= '<script>';
        $output .= 'function filterServers(status) {';
        $output .= '  var rows = document.querySelectorAll("#servers-table tbody tr");';
        $output .= '  var buttons = document.querySelectorAll(".btn-group button");';
        $output .= '  buttons.forEach(function(btn) { btn.classList.remove("active"); });';
        $output .= '  event.target.classList.add("active");';
        $output .= '  rows.forEach(function(row) {';
        $output .= '    if (status === "all" || row.getAttribute("data-status") === status) {';
        $output .= '      row.style.display = "";';
        $output .= '    } else {';
        $output .= '      row.style.display = "none";';
        $output .= '    }';
        $output .= '  });';
        $output .= '}';
        $output .= '</script>';
        
        return $output;
    }
    
    /**
     * Show create server form
     */
    public function create() {
        if ($_POST) {
            return $this->store();
        }
        
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select([
                'dcim_racks.id',
                'dcim_racks.name as rack_name',
                'dcim_racks.height_units',
                'dcim_locations.name as location_name'
            ])
            ->orderBy('dcim_locations.name')
            ->orderBy('dcim_racks.name')
            ->get();
        
        // Get WHMCS clients for assignment
        $clients = Capsule::table('tblclients')
            ->select(['id', 'firstname', 'lastname', 'email'])
            ->orderBy('firstname')
            ->orderBy('lastname')
            ->get();
        
        // Get WHMCS services
        $services = Capsule::table('tblhosting')
            ->join('tblclients', 'tblhosting.userid', '=', 'tblclients.id')
            ->select([
                'tblhosting.id',
                'tblhosting.domain',
                'tblhosting.producttype',
                'tblclients.firstname',
                'tblclients.lastname'
            ])
            ->orderBy('tblhosting.domain')
            ->get();
        
        $preselectedRack = $_GET['rack_id'] ?? '';
        $preselectedUnit = $_GET['unit'] ?? '';
        
        $output = '<div class="dcim-server-create">';
        $output .= '<h2>Add New Server</h2>';
        
        $output .= '<form method="post">';
        $output .= '<div class="row">';
        
        // Basic Information
        $output .= '<div class="col-md-12"><h4>Basic Information</h4><hr></div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="hostname">Hostname *</label>';
        $output .= '<input type="text" class="form-control" id="hostname" name="hostname" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="status">Status</label>';
        $output .= '<select class="form-control" id="status" name="status">';
        $output .= '<option value="provisioning" selected>Provisioning</option>';
        $output .= '<option value="active">Active</option>';
        $output .= '<option value="maintenance">Maintenance</option>';
        $output .= '<option value="decommissioned">Decommissioned</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Rack Assignment
        $output .= '<div class="col-md-12"><h4>Rack Assignment</h4><hr></div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="rack_id">Rack *</label>';
        $output .= '<select class="form-control" id="rack_id" name="rack_id" required onchange="updateAvailableUnits()">';
        $output .= '<option value="">Select Rack</option>';
        foreach ($racks as $rack) {
            $selected = $rack->id == $preselectedRack ? ' selected' : '';
            $output .= '<option value="' . $rack->id . '" data-height="' . $rack->height_units . '"' . $selected . '>';
            $output .= htmlspecialchars($rack->location_name . ' - ' . $rack->rack_name);
            $output .= '</option>';
        }
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="form-group">';
        $output .= '<label for="rack_position_start">Start Position (U) *</label>';
        $output .= '<input type="number" class="form-control" id="rack_position_start" name="rack_position_start" min="1" value="' . $preselectedUnit . '" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="form-group">';
        $output .= '<label for="rack_position_end">End Position (U) *</label>';
        $output .= '<input type="number" class="form-control" id="rack_position_end" name="rack_position_end" min="1" value="' . $preselectedUnit . '" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        $output .= '<div class="form-group">';
        $output .= '<button type="submit" class="btn btn-primary">Add Server</button> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=servers" class="btn btn-secondary">Cancel</a>';
        $output .= '</div>';
        
        $output .= '</form>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Store new server
     */
    private function store() {
        try {
            // Validate rack position availability
            $rackId = $_POST['rack_id'];
            $startPos = $_POST['rack_position_start'];
            $endPos = $_POST['rack_position_end'];
            
            if ($startPos > $endPos) {
                throw new \Exception('Start position cannot be greater than end position');
            }
            
            // Check for conflicts
            $conflicts = Capsule::table('dcim_servers')
                ->where('rack_id', $rackId)
                ->where('status', '!=', 'decommissioned')
                ->where(function($query) use ($startPos, $endPos) {
                    $query->whereBetween('rack_position_start', [$startPos, $endPos])
                          ->orWhereBetween('rack_position_end', [$startPos, $endPos])
                          ->orWhere(function($q) use ($startPos, $endPos) {
                              $q->where('rack_position_start', '<=', $startPos)
                                ->where('rack_position_end', '>=', $endPos);
                          });
                })
                ->exists();
            
            if ($conflicts) {
                throw new \Exception('Rack positions are already occupied');
            }
            
            $data = [
                'hostname' => $_POST['hostname'],
                'rack_id' => $rackId,
                'rack_position_start' => $startPos,
                'rack_position_end' => $endPos,
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'model' => $_POST['model'] ?? null,
                'serial_number' => $_POST['serial_number'] ?? null,
                'asset_tag' => $_POST['asset_tag'] ?? null,
                'cpu' => $_POST['cpu'] ?? null,
                'ram_gb' => !empty($_POST['ram_gb']) ? $_POST['ram_gb'] : null,
                'storage' => $_POST['storage'] ?? null,
                'network_interfaces' => $_POST['network_interfaces'] ?? null,
                'power_consumption' => !empty($_POST['power_consumption']) ? $_POST['power_consumption'] : null,
                'management_ip' => $_POST['management_ip'] ?? null,
                'whmcs_service_id' => !empty($_POST['whmcs_service_id']) ? $_POST['whmcs_service_id'] : null,
                'whmcs_client_id' => !empty($_POST['whmcs_client_id']) ? $_POST['whmcs_client_id'] : null,
                'status' => $_POST['status'],
                'notes' => $_POST['notes'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Capsule::table('dcim_servers')->insert($data);
            
            header('Location: addonmodules.php?module=dcim&action=servers&success=created');
            exit;
            
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">Error creating server: ' . $e->getMessage() . '</div>' . $this->create();
        }
    }
    
    /**
     * Get badge class for status
     */
    private function getStatusBadgeClass($status) {
        switch ($status) {
            case 'active': return 'success';
            case 'provisioning': return 'info';
            case 'maintenance': return 'warning';
            case 'decommissioned': return 'danger';
            default: return 'secondary';
        }
    }
}
