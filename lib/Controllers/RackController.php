<?php
/**
 * DCIM Rack Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM\Controllers;

use Illuminate\Database\Capsule\Manager as Capsule;

class RackController {
    
    private $vars;
    
    public function __construct($vars) {
        $this->vars = $vars;
    }
    
    /**
     * Display racks list
     */
    public function index() {
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select([
                'dcim_racks.*',
                'dcim_locations.name as location_name'
            ])
            ->orderBy('dcim_locations.name')
            ->orderBy('dcim_racks.name')
            ->get();
        
        $output = '<div class="dcim-racks">';
        $output .= '<div class="d-flex justify-content-between align-items-center mb-3">';
        $output .= '<h2>Rack Management</h2>';
        $output .= '<div>';
        $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=visual" class="btn btn-info">Visual Layout</a> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=create" class="btn btn-primary">Add Rack</a>';
        $output .= '</div>';
        $output .= '</div>';
        
        if ($racks->count() > 0) {
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-striped">';
            $output .= '<thead>';
            $output .= '<tr>';
            $output .= '<th>Name</th>';
            $output .= '<th>Location</th>';
            $output .= '<th>Height (U)</th>';
            $output .= '<th>Power Capacity</th>';
            $output .= '<th>Power Usage</th>';
            $output .= '<th>Utilization</th>';
            $output .= '<th>Status</th>';
            $output .= '<th>Actions</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody>';
            
            foreach ($racks as $rack) {
                // Calculate utilization
                $serverCount = Capsule::table('dcim_servers')
                    ->where('rack_id', $rack->id)
                    ->where('status', '!=', 'decommissioned')
                    ->count();
                
                $utilization = $rack->height_units > 0 ? round(($serverCount / $rack->height_units) * 100, 1) : 0;
                $utilizationClass = $utilization > 80 ? 'danger' : ($utilization > 60 ? 'warning' : 'success');
                
                $output .= '<tr>';
                $output .= '<td>' . htmlspecialchars($rack->name) . '</td>';
                $output .= '<td>' . htmlspecialchars($rack->location_name) . '</td>';
                $output .= '<td>' . $rack->height_units . 'U</td>';
                $output .= '<td>' . ($rack->power_capacity ? $rack->power_capacity . 'W' : 'N/A') . '</td>';
                $output .= '<td>' . ($rack->power_usage ? $rack->power_usage . 'W' : '0W') . '</td>';
                $output .= '<td>';
                $output .= '<div class="progress" style="height: 20px;">';
                $output .= '<div class="progress-bar bg-' . $utilizationClass . '" style="width: ' . $utilization . '%">' . $utilization . '%</div>';
                $output .= '</div>';
                $output .= '</td>';
                $output .= '<td><span class="badge badge-' . $this->getStatusBadgeClass($rack->status) . '">' . ucfirst($rack->status) . '</span></td>';
                $output .= '<td>';
                $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=view&id=' . $rack->id . '" class="btn btn-sm btn-info">View</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=edit&id=' . $rack->id . '" class="btn btn-sm btn-warning">Edit</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=delete&id=' . $rack->id . '" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure?\')">Delete</a>';
                $output .= '</td>';
                $output .= '</tr>';
            }
            
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
        } else {
            $output .= '<div class="alert alert-info">No racks found. <a href="addonmodules.php?module=dcim&action=racks&sub=create">Create your first rack</a>.</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Show create rack form
     */
    public function create() {
        if ($_POST) {
            return $this->store();
        }
        
        $locations = Capsule::table('dcim_locations')
            ->orderBy('name')
            ->get();
        
        $output = '<div class="dcim-rack-create">';
        $output .= '<h2>Create New Rack</h2>';
        
        $output .= '<form method="post">';
        $output .= '<div class="row">';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="name">Rack Name *</label>';
        $output .= '<input type="text" class="form-control" id="name" name="name" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="location_id">Location *</label>';
        $output .= '<select class="form-control" id="location_id" name="location_id" required>';
        $output .= '<option value="">Select Location</option>';
        foreach ($locations as $location) {
            $output .= '<option value="' . $location->id . '">' . htmlspecialchars($location->name) . ' (' . ucfirst($location->type) . ')</option>';
        }
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="height_units">Height (U) *</label>';
        $output .= '<input type="number" class="form-control" id="height_units" name="height_units" value="42" min="1" max="100" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="power_capacity">Power Capacity (W)</label>';
        $output .= '<input type="number" class="form-control" id="power_capacity" name="power_capacity" step="0.01">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="cooling_capacity">Cooling Capacity (BTU/h)</label>';
        $output .= '<input type="number" class="form-control" id="cooling_capacity" name="cooling_capacity" step="0.01">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="manufacturer">Manufacturer</label>';
        $output .= '<input type="text" class="form-control" id="manufacturer" name="manufacturer">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="model">Model</label>';
        $output .= '<input type="text" class="form-control" id="model" name="model">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="serial_number">Serial Number</label>';
        $output .= '<input type="text" class="form-control" id="serial_number" name="serial_number">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="position_x">Position X</label>';
        $output .= '<input type="number" class="form-control" id="position_x" name="position_x">';
        $output .= '<small class="form-text text-muted">X coordinate in data center layout</small>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="position_y">Position Y</label>';
        $output .= '<input type="number" class="form-control" id="position_y" name="position_y">';
        $output .= '<small class="form-text text-muted">Y coordinate in data center layout</small>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="status">Status</label>';
        $output .= '<select class="form-control" id="status" name="status">';
        $output .= '<option value="active" selected>Active</option>';
        $output .= '<option value="inactive">Inactive</option>';
        $output .= '<option value="maintenance">Maintenance</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-12">';
        $output .= '<div class="form-group">';
        $output .= '<label for="notes">Notes</label>';
        $output .= '<textarea class="form-control" id="notes" name="notes" rows="3"></textarea>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        $output .= '<div class="form-group">';
        $output .= '<button type="submit" class="btn btn-primary">Create Rack</button> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=racks" class="btn btn-secondary">Cancel</a>';
        $output .= '</div>';
        
        $output .= '</form>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Store new rack
     */
    private function store() {
        try {
            $data = [
                'name' => $_POST['name'],
                'location_id' => $_POST['location_id'],
                'height_units' => $_POST['height_units'],
                'power_capacity' => !empty($_POST['power_capacity']) ? $_POST['power_capacity'] : null,
                'cooling_capacity' => !empty($_POST['cooling_capacity']) ? $_POST['cooling_capacity'] : null,
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'model' => $_POST['model'] ?? null,
                'serial_number' => $_POST['serial_number'] ?? null,
                'position_x' => !empty($_POST['position_x']) ? $_POST['position_x'] : null,
                'position_y' => !empty($_POST['position_y']) ? $_POST['position_y'] : null,
                'status' => $_POST['status'],
                'notes' => $_POST['notes'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Capsule::table('dcim_racks')->insert($data);
            
            // Update location rack count
            $this->updateLocationRackCount($_POST['location_id']);
            
            header('Location: addonmodules.php?module=dcim&action=racks&success=created');
            exit;
            
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">Error creating rack: ' . $e->getMessage() . '</div>' . $this->create();
        }
    }
    
    /**
     * Show visual rack layout
     */
    public function visualLayout() {
        $rackId = $_GET['id'] ?? 0;

        if ($rackId) {
            return $this->showSingleRackVisual($rackId);
        }

        // Show all racks visual layout
        $locations = Capsule::table('dcim_locations')
            ->join('dcim_racks', 'dcim_locations.id', '=', 'dcim_racks.location_id')
            ->select([
                'dcim_locations.id',
                'dcim_locations.name',
                Capsule::raw('COUNT(dcim_racks.id) as rack_count')
            ])
            ->groupBy('dcim_locations.id', 'dcim_locations.name')
            ->get();

        $output = '<div class="dcim-rack-visual-layout">';
        $output .= '<h2>Rack Visual Layout</h2>';

        foreach ($locations as $location) {
            $racks = Capsule::table('dcim_racks')
                ->where('location_id', $location->id)
                ->orderBy('position_x')
                ->orderBy('position_y')
                ->orderBy('name')
                ->get();

            $output .= '<div class="location-section mb-4">';
            $output .= '<h4>' . htmlspecialchars($location->name) . ' (' . $location->rack_count . ' racks)</h4>';

            $output .= '<div class="rack-grid">';
            foreach ($racks as $rack) {
                $serverCount = Capsule::table('dcim_servers')
                    ->where('rack_id', $rack->id)
                    ->where('status', '!=', 'decommissioned')
                    ->count();

                $utilization = $rack->height_units > 0 ? round(($serverCount / $rack->height_units) * 100, 1) : 0;
                $utilizationClass = $utilization > 80 ? 'danger' : ($utilization > 60 ? 'warning' : 'success');

                $output .= '<div class="rack-mini" data-rack-id="' . $rack->id . '">';
                $output .= '<div class="rack-mini-header">' . htmlspecialchars($rack->name) . '</div>';
                $output .= '<div class="rack-mini-body">';
                $output .= '<div class="progress mb-1" style="height: 10px;">';
                $output .= '<div class="progress-bar bg-' . $utilizationClass . '" style="width: ' . $utilization . '%"></div>';
                $output .= '</div>';
                $output .= '<small>' . $utilization . '% utilized</small>';
                $output .= '</div>';
                $output .= '<div class="rack-mini-footer">';
                $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=visual&id=' . $rack->id . '" class="btn btn-xs btn-info">View</a>';
                $output .= '</div>';
                $output .= '</div>';
            }
            $output .= '</div>';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Add CSS for rack grid
        $output .= '<style>';
        $output .= '.rack-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }';
        $output .= '.rack-mini { border: 1px solid #ddd; border-radius: 5px; padding: 10px; background: #f8f9fa; cursor: pointer; }';
        $output .= '.rack-mini:hover { background: #e9ecef; }';
        $output .= '.rack-mini-header { font-weight: bold; margin-bottom: 10px; }';
        $output .= '.rack-mini-footer { margin-top: 10px; text-align: center; }';
        $output .= '</style>';

        return $output;
    }

    /**
     * Show single rack visual representation
     */
    private function showSingleRackVisual($rackId) {
        $rack = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->where('dcim_racks.id', $rackId)
            ->select([
                'dcim_racks.*',
                'dcim_locations.name as location_name'
            ])
            ->first();

        if (!$rack) {
            return '<div class="alert alert-danger">Rack not found.</div>';
        }

        // Get servers in this rack
        $servers = Capsule::table('dcim_servers')
            ->where('rack_id', $rackId)
            ->where('status', '!=', 'decommissioned')
            ->orderBy('rack_position_start')
            ->get();

        $output = '<div class="dcim-rack-visual">';
        $output .= '<div class="d-flex justify-content-between align-items-center mb-3">';
        $output .= '<h2>Rack: ' . htmlspecialchars($rack->name) . '</h2>';
        $output .= '<div>';
        $output .= '<a href="addonmodules.php?module=dcim&action=servers&sub=create&rack_id=' . $rackId . '" class="btn btn-success">Add Server</a> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=edit&id=' . $rackId . '" class="btn btn-warning">Edit Rack</a> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=visual" class="btn btn-secondary">Back to Layout</a>';
        $output .= '</div>';
        $output .= '</div>';

        // Rack information
        $output .= '<div class="row mb-4">';
        $output .= '<div class="col-md-6">';
        $output .= '<div class="card">';
        $output .= '<div class="card-header"><h5>Rack Information</h5></div>';
        $output .= '<div class="card-body">';
        $output .= '<p><strong>Location:</strong> ' . htmlspecialchars($rack->location_name) . '</p>';
        $output .= '<p><strong>Height:</strong> ' . $rack->height_units . 'U</p>';
        $output .= '<p><strong>Power Capacity:</strong> ' . ($rack->power_capacity ? $rack->power_capacity . 'W' : 'N/A') . '</p>';
        $output .= '<p><strong>Power Usage:</strong> ' . ($rack->power_usage ? $rack->power_usage . 'W' : '0W') . '</p>';
        $output .= '<p><strong>Status:</strong> <span class="badge badge-' . $this->getStatusBadgeClass($rack->status) . '">' . ucfirst($rack->status) . '</span></p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-6">';
        $output .= '<div class="card">';
        $output .= '<div class="card-header"><h5>Utilization</h5></div>';
        $output .= '<div class="card-body">';

        $serverCount = $servers->count();
        $utilization = $rack->height_units > 0 ? round(($serverCount / $rack->height_units) * 100, 1) : 0;
        $utilizationClass = $utilization > 80 ? 'danger' : ($utilization > 60 ? 'warning' : 'success');

        $output .= '<div class="progress mb-2" style="height: 30px;">';
        $output .= '<div class="progress-bar bg-' . $utilizationClass . '" style="width: ' . $utilization . '%">';
        $output .= $utilization . '%';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<p><strong>Servers:</strong> ' . $serverCount . ' / ' . $rack->height_units . ' units</p>';
        $output .= '<p><strong>Available:</strong> ' . ($rack->height_units - $serverCount) . ' units</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        // Visual rack representation
        $output .= '<div class="rack-visual" data-rack-id="' . $rackId . '">';
        $output .= '<div class="rack-header">' . htmlspecialchars($rack->name) . '</div>';
        $output .= '<div class="rack-units">';

        // Create array of occupied units
        $occupiedUnits = [];
        foreach ($servers as $server) {
            for ($u = $server->rack_position_start; $u <= $server->rack_position_end; $u++) {
                $occupiedUnits[$u] = $server;
            }
        }

        // Generate rack units (from top to bottom)
        for ($u = $rack->height_units; $u >= 1; $u--) {
            $isOccupied = isset($occupiedUnits[$u]);
            $server = $isOccupied ? $occupiedUnits[$u] : null;

            $unitClass = 'rack-unit';
            if ($isOccupied) {
                $unitClass .= ' occupied';
                if ($server->status == 'maintenance') {
                    $unitClass .= ' maintenance';
                }
            }

            $output .= '<div class="' . $unitClass . '" data-unit-number="' . $u . '"';
            if ($server) {
                $output .= ' data-server-id="' . $server->id . '"';
                $output .= ' data-server-info="' . htmlspecialchars($server->hostname . ' (' . ucfirst($server->status) . ')') . '"';
            }
            $output .= '>';
            $output .= '<div class="rack-unit-number">' . $u . '</div>';

            if ($isOccupied && $server) {
                $output .= '<div class="server-info">';
                $output .= '<span class="server-status ' . $server->status . '"></span>';
                $output .= htmlspecialchars($server->hostname);
                $output .= '</div>';
            }

            $output .= '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Update location rack count
     */
    private function updateLocationRackCount($locationId) {
        $rackCount = Capsule::table('dcim_racks')
            ->where('location_id', $locationId)
            ->count();

        Capsule::table('dcim_locations')
            ->where('id', $locationId)
            ->update(['total_racks' => $rackCount]);
    }

    /**
     * Get badge class for status
     */
    private function getStatusBadgeClass($status) {
        switch ($status) {
            case 'active': return 'success';
            case 'inactive': return 'secondary';
            case 'maintenance': return 'warning';
            default: return 'secondary';
        }
    }
}
