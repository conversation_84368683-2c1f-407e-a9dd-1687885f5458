<?php
/**
 * DCIM Location Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM\Controllers;

use Illuminate\Database\Capsule\Manager as Capsule;

class LocationController {
    
    private $vars;
    
    public function __construct($vars) {
        $this->vars = $vars;
    }
    
    /**
     * Display locations list
     */
    public function index() {
        $locations = Capsule::table('dcim_locations')
            ->orderBy('type')
            ->orderBy('name')
            ->get();
        
        $output = '<div class="dcim-locations">';
        $output .= '<div class="d-flex justify-content-between align-items-center mb-3">';
        $output .= '<h2>Location Management</h2>';
        $output .= '<a href="addonmodules.php?module=dcim&action=locations&sub=create" class="btn btn-primary">Add Location</a>';
        $output .= '</div>';
        
        if ($locations->count() > 0) {
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-striped">';
            $output .= '<thead>';
            $output .= '<tr>';
            $output .= '<th>Name</th>';
            $output .= '<th>Type</th>';
            $output .= '<th>Parent</th>';
            $output .= '<th>City</th>';
            $output .= '<th>Racks</th>';
            $output .= '<th>Status</th>';
            $output .= '<th>Actions</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody>';
            
            foreach ($locations as $location) {
                $parent = $location->parent_id ? 
                    Capsule::table('dcim_locations')->where('id', $location->parent_id)->value('name') : 
                    '-';
                
                $output .= '<tr>';
                $output .= '<td>' . htmlspecialchars($location->name) . '</td>';
                $output .= '<td><span class="badge badge-' . $this->getTypeBadgeClass($location->type) . '">' . ucfirst($location->type) . '</span></td>';
                $output .= '<td>' . htmlspecialchars($parent) . '</td>';
                $output .= '<td>' . htmlspecialchars($location->city ?? '-') . '</td>';
                $output .= '<td>' . $location->total_racks . '</td>';
                $output .= '<td><span class="badge badge-' . $this->getStatusBadgeClass($location->status) . '">' . ucfirst($location->status) . '</span></td>';
                $output .= '<td>';
                $output .= '<a href="addonmodules.php?module=dcim&action=locations&sub=view&id=' . $location->id . '" class="btn btn-sm btn-info">View</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=locations&sub=edit&id=' . $location->id . '" class="btn btn-sm btn-warning">Edit</a> ';
                $output .= '<a href="addonmodules.php?module=dcim&action=locations&sub=delete&id=' . $location->id . '" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure?\')">Delete</a>';
                $output .= '</td>';
                $output .= '</tr>';
            }
            
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
        } else {
            $output .= '<div class="alert alert-info">No locations found. <a href="addonmodules.php?module=dcim&action=locations&sub=create">Create your first location</a>.</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Show create location form
     */
    public function create() {
        if ($_POST) {
            return $this->store();
        }
        
        $parentLocations = Capsule::table('dcim_locations')
            ->whereIn('type', ['region', 'facility'])
            ->orderBy('name')
            ->get();
        
        $output = '<div class="dcim-location-create">';
        $output .= '<h2>Create New Location</h2>';
        
        $output .= '<form method="post">';
        $output .= '<div class="row">';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="name">Name *</label>';
        $output .= '<input type="text" class="form-control" id="name" name="name" required>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="type">Type *</label>';
        $output .= '<select class="form-control" id="type" name="type" required>';
        $output .= '<option value="region">Region</option>';
        $output .= '<option value="facility" selected>Facility</option>';
        $output .= '<option value="floor">Floor</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="parent_id">Parent Location</label>';
        $output .= '<select class="form-control" id="parent_id" name="parent_id">';
        $output .= '<option value="">None</option>';
        foreach ($parentLocations as $parent) {
            $output .= '<option value="' . $parent->id . '">' . htmlspecialchars($parent->name) . ' (' . ucfirst($parent->type) . ')</option>';
        }
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="status">Status</label>';
        $output .= '<select class="form-control" id="status" name="status">';
        $output .= '<option value="active" selected>Active</option>';
        $output .= '<option value="inactive">Inactive</option>';
        $output .= '<option value="maintenance">Maintenance</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-12">';
        $output .= '<div class="form-group">';
        $output .= '<label for="address">Address</label>';
        $output .= '<textarea class="form-control" id="address" name="address" rows="3"></textarea>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="city">City</label>';
        $output .= '<input type="text" class="form-control" id="city" name="city">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="state">State/Province</label>';
        $output .= '<input type="text" class="form-control" id="state" name="state">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-4">';
        $output .= '<div class="form-group">';
        $output .= '<label for="country">Country</label>';
        $output .= '<input type="text" class="form-control" id="country" name="country">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="contact_name">Contact Name</label>';
        $output .= '<input type="text" class="form-control" id="contact_name" name="contact_name">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="contact_email">Contact Email</label>';
        $output .= '<input type="email" class="form-control" id="contact_email" name="contact_email">';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-12">';
        $output .= '<div class="form-group">';
        $output .= '<label for="notes">Notes</label>';
        $output .= '<textarea class="form-control" id="notes" name="notes" rows="3"></textarea>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        $output .= '<div class="form-group">';
        $output .= '<button type="submit" class="btn btn-primary">Create Location</button> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=locations" class="btn btn-secondary">Cancel</a>';
        $output .= '</div>';
        
        $output .= '</form>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Store new location
     */
    private function store() {
        try {
            $data = [
                'name' => $_POST['name'],
                'type' => $_POST['type'],
                'parent_id' => !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
                'address' => $_POST['address'] ?? null,
                'city' => $_POST['city'] ?? null,
                'state' => $_POST['state'] ?? null,
                'country' => $_POST['country'] ?? null,
                'contact_name' => $_POST['contact_name'] ?? null,
                'contact_email' => $_POST['contact_email'] ?? null,
                'status' => $_POST['status'],
                'notes' => $_POST['notes'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Capsule::table('dcim_locations')->insert($data);
            
            header('Location: addonmodules.php?module=dcim&action=locations&success=created');
            exit;
            
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">Error creating location: ' . $e->getMessage() . '</div>' . $this->create();
        }
    }
    
    /**
     * Show edit location form
     */
    public function edit() {
        $id = $_GET['id'] ?? 0;
        $location = Capsule::table('dcim_locations')->where('id', $id)->first();

        if (!$location) {
            return '<div class="alert alert-danger">Location not found.</div>';
        }

        if ($_POST) {
            return $this->update($id);
        }

        $parentLocations = Capsule::table('dcim_locations')
            ->whereIn('type', ['region', 'facility'])
            ->where('id', '!=', $id)
            ->orderBy('name')
            ->get();

        $output = '<div class="dcim-location-edit">';
        $output .= '<h2>Edit Location: ' . htmlspecialchars($location->name) . '</h2>';

        $output .= '<form method="post">';
        $output .= '<div class="row">';

        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="name">Name *</label>';
        $output .= '<input type="text" class="form-control" id="name" name="name" value="' . htmlspecialchars($location->name) . '" required>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="type">Type *</label>';
        $output .= '<select class="form-control" id="type" name="type" required>';
        $output .= '<option value="region"' . ($location->type == 'region' ? ' selected' : '') . '>Region</option>';
        $output .= '<option value="facility"' . ($location->type == 'facility' ? ' selected' : '') . '>Facility</option>';
        $output .= '<option value="floor"' . ($location->type == 'floor' ? ' selected' : '') . '>Floor</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="parent_id">Parent Location</label>';
        $output .= '<select class="form-control" id="parent_id" name="parent_id">';
        $output .= '<option value="">None</option>';
        foreach ($parentLocations as $parent) {
            $selected = $parent->id == $location->parent_id ? ' selected' : '';
            $output .= '<option value="' . $parent->id . '"' . $selected . '>' . htmlspecialchars($parent->name) . ' (' . ucfirst($parent->type) . ')</option>';
        }
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-6">';
        $output .= '<div class="form-group">';
        $output .= '<label for="status">Status</label>';
        $output .= '<select class="form-control" id="status" name="status">';
        $output .= '<option value="active"' . ($location->status == 'active' ? ' selected' : '') . '>Active</option>';
        $output .= '<option value="inactive"' . ($location->status == 'inactive' ? ' selected' : '') . '>Inactive</option>';
        $output .= '<option value="maintenance"' . ($location->status == 'maintenance' ? ' selected' : '') . '>Maintenance</option>';
        $output .= '</select>';
        $output .= '</div>';
        $output .= '</div>';

        // Add remaining form fields similar to create method
        $output .= '<div class="col-md-12">';
        $output .= '<div class="form-group">';
        $output .= '<label for="address">Address</label>';
        $output .= '<textarea class="form-control" id="address" name="address" rows="3">' . htmlspecialchars($location->address ?? '') . '</textarea>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '</div>';

        $output .= '<div class="form-group">';
        $output .= '<button type="submit" class="btn btn-primary">Update Location</button> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=locations" class="btn btn-secondary">Cancel</a>';
        $output .= '</div>';

        $output .= '</form>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Update location
     */
    private function update($id) {
        try {
            $data = [
                'name' => $_POST['name'],
                'type' => $_POST['type'],
                'parent_id' => !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
                'address' => $_POST['address'] ?? null,
                'status' => $_POST['status'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Capsule::table('dcim_locations')->where('id', $id)->update($data);

            header('Location: addonmodules.php?module=dcim&action=locations&success=updated');
            exit;

        } catch (\Exception $e) {
            return '<div class="alert alert-danger">Error updating location: ' . $e->getMessage() . '</div>' . $this->edit();
        }
    }

    /**
     * View location details
     */
    public function view() {
        $id = $_GET['id'] ?? 0;
        $location = Capsule::table('dcim_locations')->where('id', $id)->first();

        if (!$location) {
            return '<div class="alert alert-danger">Location not found.</div>';
        }

        $racks = Capsule::table('dcim_racks')->where('location_id', $id)->get();

        $output = '<div class="dcim-location-view">';
        $output .= '<div class="d-flex justify-content-between align-items-center mb-3">';
        $output .= '<h2>' . htmlspecialchars($location->name) . '</h2>';
        $output .= '<div>';
        $output .= '<a href="addonmodules.php?module=dcim&action=locations&sub=edit&id=' . $id . '" class="btn btn-warning">Edit</a> ';
        $output .= '<a href="addonmodules.php?module=dcim&action=locations" class="btn btn-secondary">Back to List</a>';
        $output .= '</div>';
        $output .= '</div>';

        // Location details
        $output .= '<div class="card mb-4">';
        $output .= '<div class="card-header"><h5>Location Details</h5></div>';
        $output .= '<div class="card-body">';
        $output .= '<div class="row">';
        $output .= '<div class="col-md-6">';
        $output .= '<p><strong>Type:</strong> ' . ucfirst($location->type) . '</p>';
        $output .= '<p><strong>Status:</strong> <span class="badge badge-' . $this->getStatusBadgeClass($location->status) . '">' . ucfirst($location->status) . '</span></p>';
        $output .= '<p><strong>Total Racks:</strong> ' . $location->total_racks . '</p>';
        $output .= '</div>';
        $output .= '<div class="col-md-6">';
        $output .= '<p><strong>City:</strong> ' . htmlspecialchars($location->city ?? 'N/A') . '</p>';
        $output .= '<p><strong>Contact:</strong> ' . htmlspecialchars($location->contact_name ?? 'N/A') . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        // Racks in this location
        $output .= '<div class="card">';
        $output .= '<div class="card-header">';
        $output .= '<div class="d-flex justify-content-between align-items-center">';
        $output .= '<h5>Racks in this Location</h5>';
        $output .= '<a href="addonmodules.php?module=dcim&action=racks&sub=create&location_id=' . $id . '" class="btn btn-sm btn-primary">Add Rack</a>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<div class="card-body">';

        if ($racks->count() > 0) {
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-sm">';
            $output .= '<thead>';
            $output .= '<tr><th>Name</th><th>Height (U)</th><th>Power Capacity</th><th>Status</th><th>Actions</th></tr>';
            $output .= '</thead>';
            $output .= '<tbody>';
            foreach ($racks as $rack) {
                $output .= '<tr>';
                $output .= '<td>' . htmlspecialchars($rack->name) . '</td>';
                $output .= '<td>' . $rack->height_units . 'U</td>';
                $output .= '<td>' . ($rack->power_capacity ?? 'N/A') . '</td>';
                $output .= '<td><span class="badge badge-' . $this->getStatusBadgeClass($rack->status) . '">' . ucfirst($rack->status) . '</span></td>';
                $output .= '<td><a href="addonmodules.php?module=dcim&action=racks&sub=view&id=' . $rack->id . '" class="btn btn-sm btn-info">View</a></td>';
                $output .= '</tr>';
            }
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
        } else {
            $output .= '<p class="text-muted">No racks in this location.</p>';
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Delete location
     */
    public function delete() {
        $id = $_GET['id'] ?? 0;

        try {
            // Check if location has racks
            $rackCount = Capsule::table('dcim_racks')->where('location_id', $id)->count();

            if ($rackCount > 0) {
                header('Location: addonmodules.php?module=dcim&action=locations&error=has_racks');
                exit;
            }

            Capsule::table('dcim_locations')->where('id', $id)->delete();

            header('Location: addonmodules.php?module=dcim&action=locations&success=deleted');
            exit;

        } catch (\Exception $e) {
            header('Location: addonmodules.php?module=dcim&action=locations&error=delete_failed');
            exit;
        }
    }

    /**
     * Get badge class for location type
     */
    private function getTypeBadgeClass($type) {
        switch ($type) {
            case 'region': return 'primary';
            case 'facility': return 'success';
            case 'floor': return 'info';
            default: return 'secondary';
        }
    }

    /**
     * Get badge class for status
     */
    private function getStatusBadgeClass($status) {
        switch ($status) {
            case 'active': return 'success';
            case 'inactive': return 'secondary';
            case 'maintenance': return 'warning';
            default: return 'secondary';
        }
    }
}
