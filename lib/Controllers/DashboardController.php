<?php
/**
 * DCIM Dashboard Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM\Controllers;

use Illuminate\Database\Capsule\Manager as Capsule;

class DashboardController {

    private $vars;
    private $moduleLink;

    public function __construct($vars) {
        $this->vars = $vars;
        $this->moduleLink = $vars['modulelink'] ?? 'addonmodules.php?module=dcim';
    }

    /**
     * Generate proper module URL
     */
    private function getModuleUrl($action = '', $params = []) {
        $url = $this->moduleLink;

        if ($action) {
            $url .= '&action=' . urlencode($action);
        }

        foreach ($params as $key => $value) {
            $url .= '&' . urlencode($key) . '=' . urlencode($value);
        }

        return $url;
    }
    
    /**
     * Display main dashboard
     */
    public function index() {
        try {
            // Get statistics
            $stats = $this->getStatistics();

            // Get recent activities
            $recentActivities = $this->getRecentActivities();

            // Get alerts
            $alerts = $this->getAlerts();
        
        $output = '<div class="dcim-dashboard">';
        $output .= '<h1>DCIM Dashboard</h1>';
        
        // Statistics cards
        $output .= '<div class="row">';
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-primary text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $stats['total_locations'] . '</h4>';
        $output .= '<p>Total Locations</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-success text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $stats['total_racks'] . '</h4>';
        $output .= '<p>Total Racks</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-info text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $stats['total_servers'] . '</h4>';
        $output .= '<p>Total Servers</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="card bg-warning text-white">';
        $output .= '<div class="card-body">';
        $output .= '<h4>' . $stats['ip_utilization'] . '%</h4>';
        $output .= '<p>IP Utilization</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Quick actions
        $output .= '<div class="row mt-4">';
        $output .= '<div class="col-md-12">';
        $output .= '<div class="card">';
        $output .= '<div class="card-header">';
        $output .= '<h5>Quick Actions</h5>';
        $output .= '</div>';
        $output .= '<div class="card-body">';
        $output .= '<a href="' . $this->getModuleUrl('locations', ['sub' => 'create']) . '" class="btn btn-primary">Add Location</a> ';
        $output .= '<a href="' . $this->getModuleUrl('racks', ['sub' => 'create']) . '" class="btn btn-success">Add Rack</a> ';
        $output .= '<a href="' . $this->getModuleUrl('servers', ['sub' => 'create']) . '" class="btn btn-info">Add Server</a> ';
        $output .= '<a href="' . $this->getModuleUrl('ipam', ['sub' => 'create_subnet']) . '" class="btn btn-warning">Create Subnet</a>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Recent activities
        $output .= '<div class="row mt-4">';
        $output .= '<div class="col-md-6">';
        $output .= '<div class="card">';
        $output .= '<div class="card-header">';
        $output .= '<h5>Recent Activities</h5>';
        $output .= '</div>';
        $output .= '<div class="card-body">';
        if (!empty($recentActivities)) {
            $output .= '<ul class="list-unstyled">';
            foreach ($recentActivities as $activity) {
                $output .= '<li class="mb-2">';
                $output .= '<small class="text-muted">' . $activity['date'] . '</small><br>';
                $output .= $activity['description'];
                $output .= '</li>';
            }
            $output .= '</ul>';
        } else {
            $output .= '<p class="text-muted">No recent activities</p>';
        }
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Alerts
        $output .= '<div class="col-md-6">';
        $output .= '<div class="card">';
        $output .= '<div class="card-header">';
        $output .= '<h5>System Alerts</h5>';
        $output .= '</div>';
        $output .= '<div class="card-body">';
        if (!empty($alerts)) {
            foreach ($alerts as $alert) {
                $output .= '<div class="alert alert-' . $alert['type'] . '">';
                $output .= $alert['message'];
                $output .= '</div>';
            }
        } else {
            $output .= '<p class="text-success">No alerts</p>';
        }
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';

        return $output;

        } catch (Exception $e) {
            return '<div class="alert alert-danger">Dashboard Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    
    /**
     * Get system statistics
     */
    private function getStatistics() {
        // Return default stats for now to avoid database issues
        $stats = [
            'total_locations' => 0,
            'total_racks' => 0,
            'total_servers' => 0,
            'total_switches' => 0,
            'ip_utilization' => 0
        ];

        try {
            // Check if tables exist before querying
            if (Capsule::schema()->hasTable('dcim_locations')) {
                $stats['total_locations'] = Capsule::table('dcim_locations')->count();
            }
            if (Capsule::schema()->hasTable('dcim_racks')) {
                $stats['total_racks'] = Capsule::table('dcim_racks')->count();
            }
            if (Capsule::schema()->hasTable('dcim_servers')) {
                $stats['total_servers'] = Capsule::table('dcim_servers')->count();
            }
            if (Capsule::schema()->hasTable('dcim_switches')) {
                $stats['total_switches'] = Capsule::table('dcim_switches')->count();
            }

            // Calculate IP utilization
            if (Capsule::schema()->hasTable('dcim_ip_assignments')) {
                $totalIPs = Capsule::table('dcim_ip_assignments')->count();
                $usedIPs = Capsule::table('dcim_ip_assignments')
                    ->where('status', 'active')
                    ->count();

                $stats['ip_utilization'] = $totalIPs > 0 ? round(($usedIPs / $totalIPs) * 100, 1) : 0;
            }

        } catch (\Exception $e) {
            // Keep default stats on error
            error_log("DCIM Dashboard Error: " . $e->getMessage());
        }

        return $stats;
    }
    
    /**
     * Get recent activities
     */
    private function getRecentActivities() {
        // This would typically come from an activity log table
        // For now, return sample data
        return [
            [
                'date' => date('Y-m-d H:i:s'),
                'description' => 'Server SRV001 added to Rack R001'
            ],
            [
                'date' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'description' => 'New subnet ***********/24 created'
            ],
            [
                'date' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'description' => 'Location DC-East updated'
            ]
        ];
    }
    
    /**
     * Get system alerts
     */
    private function getAlerts() {
        $alerts = [];
        
        try {
            // Check for racks with high utilization
            $highUtilizationRacks = Capsule::table('dcim_racks')
                ->select('name')
                ->whereRaw('(SELECT COUNT(*) FROM dcim_servers WHERE rack_id = dcim_racks.id) / height_units > 0.8')
                ->get();
            
            foreach ($highUtilizationRacks as $rack) {
                $alerts[] = [
                    'type' => 'warning',
                    'message' => 'Rack ' . $rack->name . ' is over 80% utilized'
                ];
            }
            
            // Check for servers without IP assignments
            $serversWithoutIP = Capsule::table('dcim_servers')
                ->leftJoin('dcim_ip_assignments', function($join) {
                    $join->on('dcim_servers.id', '=', 'dcim_ip_assignments.assigned_to_id')
                         ->where('dcim_ip_assignments.assignment_type', '=', 'server');
                })
                ->whereNull('dcim_ip_assignments.id')
                ->count();
            
            if ($serversWithoutIP > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'message' => $serversWithoutIP . ' servers do not have IP assignments'
                ];
            }
            
        } catch (\Exception $e) {
            // Handle database errors gracefully
        }
        
        return $alerts;
    }
}
