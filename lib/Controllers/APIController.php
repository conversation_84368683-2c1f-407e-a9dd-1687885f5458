<?php
/**
 * DCIM API Controller
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM\Controllers;

use Illuminate\Database\Capsule\Manager as Capsule;

class APIController {
    
    private $vars;
    
    public function __construct($vars) {
        $this->vars = $vars;
    }
    
    /**
     * Display API documentation and management
     */
    public function index() {
        $output = '<div class="dcim-api">';
        $output .= '<h2>DCIM API Documentation</h2>';
        
        // API Status
        $apiEnabled = Capsule::table('tbladdonmodules')
            ->where('module', 'dcim')
            ->where('setting', 'api_access')
            ->where('value', 'on')
            ->exists();
        
        $output .= '<div class="alert alert-' . ($apiEnabled ? 'success' : 'warning') . '">';
        $output .= '<strong>API Status:</strong> ' . ($apiEnabled ? 'Enabled' : 'Disabled');
        if (!$apiEnabled) {
            $output .= ' - <a href="configaddonmods.php">Enable in module configuration</a>';
        }
        $output .= '</div>';
        
        // API Endpoints
        $output .= '<div class="card mb-4">';
        $output .= '<div class="card-header"><h4>Available Endpoints</h4></div>';
        $output .= '<div class="card-body">';
        
        $endpoints = [
            'GET /api/dcim/locations' => 'List all locations',
            'GET /api/dcim/locations/{id}' => 'Get specific location',
            'POST /api/dcim/locations' => 'Create new location',
            'PUT /api/dcim/locations/{id}' => 'Update location',
            'DELETE /api/dcim/locations/{id}' => 'Delete location',
            
            'GET /api/dcim/racks' => 'List all racks',
            'GET /api/dcim/racks/{id}' => 'Get specific rack',
            'POST /api/dcim/racks' => 'Create new rack',
            'PUT /api/dcim/racks/{id}' => 'Update rack',
            'DELETE /api/dcim/racks/{id}' => 'Delete rack',
            
            'GET /api/dcim/servers' => 'List all servers',
            'GET /api/dcim/servers/{id}' => 'Get specific server',
            'POST /api/dcim/servers' => 'Create new server',
            'PUT /api/dcim/servers/{id}' => 'Update server',
            'DELETE /api/dcim/servers/{id}' => 'Delete server',
            
            'GET /api/dcim/subnets' => 'List all subnets',
            'GET /api/dcim/subnets/{id}' => 'Get specific subnet',
            'POST /api/dcim/subnets' => 'Create new subnet',
            'PUT /api/dcim/subnets/{id}' => 'Update subnet',
            'DELETE /api/dcim/subnets/{id}' => 'Delete subnet',
            
            'GET /api/dcim/ip-assignments' => 'List IP assignments',
            'POST /api/dcim/ip-assignments' => 'Assign IP address',
            'PUT /api/dcim/ip-assignments/{id}' => 'Update IP assignment',
            'DELETE /api/dcim/ip-assignments/{id}' => 'Release IP assignment',
        ];
        
        $output .= '<div class="table-responsive">';
        $output .= '<table class="table table-striped">';
        $output .= '<thead>';
        $output .= '<tr><th>Endpoint</th><th>Description</th></tr>';
        $output .= '</thead>';
        $output .= '<tbody>';
        
        foreach ($endpoints as $endpoint => $description) {
            $method = explode(' ', $endpoint)[0];
            $methodClass = $this->getMethodClass($method);
            
            $output .= '<tr>';
            $output .= '<td><span class="badge badge-' . $methodClass . '">' . $method . '</span> <code>' . explode(' ', $endpoint, 2)[1] . '</code></td>';
            $output .= '<td>' . htmlspecialchars($description) . '</td>';
            $output .= '</tr>';
        }
        
        $output .= '</tbody>';
        $output .= '</table>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Authentication
        $output .= '<div class="card mb-4">';
        $output .= '<div class="card-header"><h4>Authentication</h4></div>';
        $output .= '<div class="card-body">';
        $output .= '<p>The DCIM API uses WHMCS API authentication. Include the following headers in your requests:</p>';
        $output .= '<pre><code>Authorization: Bearer YOUR_API_TOKEN
Content-Type: application/json</code></pre>';
        $output .= '<p>API tokens can be generated in the WHMCS admin area under <strong>Setup > Staff Management > API Credentials</strong>.</p>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Example Requests
        $output .= '<div class="card mb-4">';
        $output .= '<div class="card-header"><h4>Example Requests</h4></div>';
        $output .= '<div class="card-body">';
        
        $output .= '<h5>Get All Locations</h5>';
        $output .= '<pre><code>curl -X GET "https://your-whmcs.com/api/dcim/locations" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json"</code></pre>';
        
        $output .= '<h5>Create New Server</h5>';
        $output .= '<pre><code>curl -X POST "https://your-whmcs.com/api/dcim/servers" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d \'{
    "hostname": "server001.example.com",
    "rack_id": 1,
    "rack_position_start": 10,
    "rack_position_end": 11,
    "status": "active",
    "cpu": "Intel Xeon E5-2680",
    "ram_gb": 64,
    "storage": "2x 1TB SSD RAID1"
  }\'</code></pre>';
        
        $output .= '<h5>Assign IP Address</h5>';
        $output .= '<pre><code>curl -X POST "https://your-whmcs.com/api/dcim/ip-assignments" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d \'{
    "subnet_id": 1,
    "ip_address": "*************",
    "assignment_type": "server",
    "assigned_to_id": 1,
    "hostname": "server001.example.com"
  }\'</code></pre>';
        
        $output .= '</div>';
        $output .= '</div>';
        
        // Response Format
        $output .= '<div class="card mb-4">';
        $output .= '<div class="card-header"><h4>Response Format</h4></div>';
        $output .= '<div class="card-body">';
        $output .= '<p>All API responses follow this format:</p>';
        $output .= '<pre><code>{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Success message",
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "per_page": 25,
    "total_items": 125
  }
}</code></pre>';
        
        $output .= '<p>Error responses:</p>';
        $output .= '<pre><code>{
  "success": false,
  "error": "Error message",
  "code": 400
}</code></pre>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Rate Limiting
        $output .= '<div class="card mb-4">';
        $output .= '<div class="card-header"><h4>Rate Limiting</h4></div>';
        $output .= '<div class="card-body">';
        $output .= '<p>The API is rate limited to:</p>';
        $output .= '<ul>';
        $output .= '<li><strong>100 requests per minute</strong> for read operations (GET)</li>';
        $output .= '<li><strong>50 requests per minute</strong> for write operations (POST, PUT, DELETE)</li>';
        $output .= '</ul>';
        $output .= '<p>Rate limit headers are included in all responses:</p>';
        $output .= '<pre><code>X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200</code></pre>';
        $output .= '</div>';
        $output .= '</div>';
        
        // Error Codes
        $output .= '<div class="card">';
        $output .= '<div class="card-header"><h4>HTTP Status Codes</h4></div>';
        $output .= '<div class="card-body">';
        $output .= '<div class="table-responsive">';
        $output .= '<table class="table table-striped">';
        $output .= '<thead>';
        $output .= '<tr><th>Code</th><th>Description</th></tr>';
        $output .= '</thead>';
        $output .= '<tbody>';
        $output .= '<tr><td>200</td><td>OK - Request successful</td></tr>';
        $output .= '<tr><td>201</td><td>Created - Resource created successfully</td></tr>';
        $output .= '<tr><td>400</td><td>Bad Request - Invalid request data</td></tr>';
        $output .= '<tr><td>401</td><td>Unauthorized - Invalid or missing API token</td></tr>';
        $output .= '<tr><td>403</td><td>Forbidden - Insufficient permissions</td></tr>';
        $output .= '<tr><td>404</td><td>Not Found - Resource not found</td></tr>';
        $output .= '<tr><td>422</td><td>Unprocessable Entity - Validation errors</td></tr>';
        $output .= '<tr><td>429</td><td>Too Many Requests - Rate limit exceeded</td></tr>';
        $output .= '<tr><td>500</td><td>Internal Server Error - Server error</td></tr>';
        $output .= '</tbody>';
        $output .= '</table>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Get CSS class for HTTP method
     */
    private function getMethodClass($method) {
        switch ($method) {
            case 'GET': return 'primary';
            case 'POST': return 'success';
            case 'PUT': return 'warning';
            case 'DELETE': return 'danger';
            default: return 'secondary';
        }
    }
}
