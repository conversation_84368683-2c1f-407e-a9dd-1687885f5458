<?php
/**
 * DCIM URL Helper
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

namespace DCIM;

class UrlHelper {
    
    private static $moduleLink = null;
    
    /**
     * Set the module link from WHMCS vars
     */
    public static function setModuleLink($moduleLink) {
        self::$moduleLink = $moduleLink;
    }
    
    /**
     * Generate proper module URL
     */
    public static function getModuleUrl($action = '', $params = []) {
        $url = self::$moduleLink ?: 'addonmodules.php?module=dcim';
        
        if ($action) {
            $url .= '&action=' . urlencode($action);
        }
        
        foreach ($params as $key => $value) {
            $url .= '&' . urlencode($key) . '=' . urlencode($value);
        }
        
        return $url;
    }
    
    /**
     * Get the correct asset path
     */
    public static function getAssetPath($asset) {
        // Detect if WHMCS is in a subdirectory
        $basePath = '';
        if (isset($_SERVER['REQUEST_URI'])) {
            $requestUri = $_SERVER['REQUEST_URI'];
            if (strpos($requestUri, '/whmcs/') !== false) {
                $basePath = '/whmcs';
            }
        }
        
        return $basePath . '/modules/addons/dcim/assets/' . $asset;
    }
    
    /**
     * Generate redirect URL
     */
    public static function redirect($action = '', $params = []) {
        $url = self::getModuleUrl($action, $params);
        header('Location: ' . $url);
        exit;
    }
}
