# DCIM - Data Center Infrastructure Management for WHMCS

A comprehensive WHMCS addon module that provides hosting providers and data center operators with complete physical infrastructure management capabilities integrated directly into the WHMCS admin interface.

## Features

### 🏢 Location Management
- Hierarchical location structure (regions > facilities > floors)
- Complete location details with contact information
- Capacity tracking and management
- Status monitoring

### 🗄️ Rack Management
- Visual rack layout representation with U-space visualization
- Rack specifications tracking (height, power, cooling)
- Occupancy status and utilization monitoring
- Position-based rack organization

### 🖥️ Server Management
- Comprehensive server inventory with hardware specifications
- Rack position assignment with visual representation
- Server lifecycle management (provisioning, active, maintenance, decommissioned)
- WHMCS service and client integration

### 🔌 Network Switch Management
- Switch inventory with port management
- VLAN configuration tracking
- Port connectivity mapping
- Network device monitoring

### 🌐 IPAM (IP Address Management)
- IPv4 and IPv6 subnet management with CIDR notation
- Automatic IP allocation and manual assignment
- IP usage statistics and availability reporting
- Subnet hierarchy and VLAN association
- IP reservation and blacklisting functionality

### 🔗 WHMCS Integration
- Seamless integration into WHMCS admin area
- Link physical servers to WHMCS products and services
- Associate IP addresses with WHMCS clients and services
- Automated IP allocation for new services
- Client area IP management (optional)

### 🚀 API Support
- Comprehensive REST API for external integrations
- Full CRUD operations for all resources
- Rate limiting and authentication
- Detailed API documentation

## Requirements

- WHMCS 7.0 or higher
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Modern web browser with JavaScript enabled

## Installation

1. **Download and Extract**
   ```bash
   # Extract the module to your WHMCS modules directory
   unzip dcim-module.zip -d /path/to/whmcs/modules/addons/
   ```

2. **Set Permissions**
   ```bash
   chmod -R 755 /path/to/whmcs/modules/addons/dcim/
   ```

3. **Activate Module**
   - Log into WHMCS admin area
   - Navigate to **Setup > Addon Modules**
   - Find "DCIM - Data Center Infrastructure Management"
   - Click **Activate**

4. **Configure Module**
   - Click **Configure** next to the DCIM module
   - Set your preferred options:
     - Enable IPAM functionality
     - Enable rack visualization
     - Set default rack height
     - Enable API access
     - Configure auto IP allocation

5. **Set Permissions**
   - Configure which admin roles can access the module
   - Set appropriate permissions for different user levels

## Quick Start Guide

### 1. Create Your First Location
1. Navigate to **Addons > DCIM > Locations**
2. Click **Add Location**
3. Fill in location details (name, type, address, contact info)
4. Save the location

### 2. Add Racks to Your Location
1. Go to **Addons > DCIM > Racks**
2. Click **Add Rack**
3. Select the location and specify rack details
4. Set rack height (default 42U) and power capacity
5. Save the rack

### 3. Add Servers to Racks
1. Navigate to **Addons > DCIM > Servers**
2. Click **Add Server**
3. Enter server hostname and specifications
4. Assign to rack and specify U positions
5. Optionally link to WHMCS service/client
6. Save the server

### 4. Create IP Subnets
1. Go to **Addons > DCIM > IP Management**
2. Click **Create Subnet**
3. Enter subnet details (name, network, CIDR)
4. Set gateway and DNS servers
5. Save - IP addresses will be auto-generated

### 5. Assign IP Addresses
1. In the IP Management section, click on any available IP
2. Select assignment type (server, client, service, etc.)
3. Enter hostname and description
4. Save the assignment

## Configuration Options

### Module Settings
- **Enable IPAM**: Turn on/off IP address management features
- **Enable Rack Visualization**: Enable visual rack layout displays
- **Default Rack Height**: Set default height for new racks (in U)
- **Enable API Access**: Allow external API access
- **Auto IP Allocation**: Automatically assign IPs to new services

### WHMCS Integration
The module integrates with WHMCS in several ways:
- **Service Linking**: Connect servers to WHMCS hosting services
- **Client Association**: Link IP assignments to WHMCS clients
- **Automated Workflows**: Auto-assign IPs when services are created
- **Admin Interface**: Seamless integration into WHMCS admin area

## API Usage

### Authentication
All API requests require WHMCS API authentication:
```bash
curl -X GET "https://your-whmcs.com/api/dcim/locations" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json"
```

### Example API Calls

**Get all servers:**
```bash
GET /api/dcim/servers
```

**Create a new rack:**
```bash
POST /api/dcim/racks
{
  "name": "Rack-A01",
  "location_id": 1,
  "height_units": 42,
  "power_capacity": 5000
}
```

**Assign IP address:**
```bash
POST /api/dcim/ip-assignments
{
  "subnet_id": 1,
  "ip_address": "*************",
  "assignment_type": "server",
  "hostname": "server001.example.com"
}
```

## Troubleshooting

### Common Issues

**Module won't activate:**
- Check PHP version (7.4+ required)
- Verify file permissions
- Check WHMCS error logs

**Database errors:**
- Ensure MySQL user has CREATE/ALTER permissions
- Check database connection settings
- Verify WHMCS database integrity

**Visual elements not loading:**
- Clear browser cache
- Check for JavaScript errors in browser console
- Verify CSS/JS files are accessible

### Support

For support and bug reports:
- Check the documentation in the `docs/` directory
- Review the API documentation at `/addonmodules.php?module=dcim&action=api`
- Submit issues through your support channel

## License

This module is licensed under the MIT License. See the LICENSE file for details.

## Changelog

### Version 1.0.0
- Initial release
- Complete DCIM functionality
- WHMCS integration
- REST API support
- Visual rack management
- Comprehensive IPAM

## Contributing

We welcome contributions! Please read our contributing guidelines and submit pull requests for any improvements.

---

**DCIM Module for WHMCS** - Professional data center infrastructure management integrated with your WHMCS installation.
