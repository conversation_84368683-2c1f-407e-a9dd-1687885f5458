<?php
/**
 * DCIM Database Test Script
 * 
 * This script tests the database schema creation independently
 * Run this to verify the database structure works before activating the module
 */

// Simulate WHMCS environment
if (!defined("WHMCS")) {
    define("WHMCS", true);
}

// Include WHMCS configuration (adjust path as needed)
$whmcsPath = dirname(dirname(dirname(__DIR__)));
if (file_exists($whmcsPath . '/configuration.php')) {
    require_once $whmcsPath . '/configuration.php';
    require_once $whmcsPath . '/init.php';
} else {
    echo "Error: Could not find WHMCS configuration file.\n";
    echo "Please run this script from the correct module directory.\n";
    exit(1);
}

// Include our database class
require_once __DIR__ . '/lib/Database.php';

echo "DCIM Database Schema Test\n";
echo "=========================\n\n";

try {
    $database = new DCIM\Database();
    
    echo "Testing database table creation...\n";
    $result = $database->createTables();
    
    if ($result['success']) {
        echo "✓ Database tables created successfully!\n\n";
        
        // Test table existence
        $tables = [
            'dcim_locations',
            'dcim_racks',
            'dcim_servers',
            'dcim_switches',
            'dcim_switch_ports',
            'dcim_subnets',
            'dcim_ip_assignments'
        ];
        
        echo "Verifying table creation:\n";
        foreach ($tables as $table) {
            if (\Illuminate\Database\Capsule\Manager::schema()->hasTable($table)) {
                echo "✓ Table '$table' exists\n";
            } else {
                echo "✗ Table '$table' missing\n";
            }
        }
        
        echo "\nTesting foreign key constraints:\n";
        
        // Test inserting sample data to verify constraints work
        try {
            // Insert a location
            $locationId = \Illuminate\Database\Capsule\Manager::table('dcim_locations')->insertGetId([
                'name' => 'Test Location',
                'type' => 'facility',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "✓ Location inserted (ID: $locationId)\n";
            
            // Insert a rack
            $rackId = \Illuminate\Database\Capsule\Manager::table('dcim_racks')->insertGetId([
                'name' => 'Test Rack',
                'location_id' => $locationId,
                'height_units' => 42,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "✓ Rack inserted (ID: $rackId)\n";
            
            // Insert a server
            $serverId = \Illuminate\Database\Capsule\Manager::table('dcim_servers')->insertGetId([
                'hostname' => 'test-server.example.com',
                'rack_id' => $rackId,
                'rack_position_start' => 1,
                'rack_position_end' => 2,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "✓ Server inserted (ID: $serverId)\n";
            
            // Insert a subnet
            $subnetId = \Illuminate\Database\Capsule\Manager::table('dcim_subnets')->insertGetId([
                'name' => 'Test Subnet',
                'network' => '***********',
                'prefix_length' => 24,
                'ip_version' => '4',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "✓ Subnet inserted (ID: $subnetId)\n";
            
            // Insert an IP assignment
            $ipId = \Illuminate\Database\Capsule\Manager::table('dcim_ip_assignments')->insertGetId([
                'subnet_id' => $subnetId,
                'ip_address' => '*************',
                'assignment_type' => 'server',
                'assigned_to_id' => $serverId,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "✓ IP assignment inserted (ID: $ipId)\n";
            
            echo "✓ Foreign key constraints working correctly\n";
            
            // Clean up test data
            \Illuminate\Database\Capsule\Manager::table('dcim_ip_assignments')->where('id', $ipId)->delete();
            \Illuminate\Database\Capsule\Manager::table('dcim_servers')->where('id', $serverId)->delete();
            \Illuminate\Database\Capsule\Manager::table('dcim_racks')->where('id', $rackId)->delete();
            \Illuminate\Database\Capsule\Manager::table('dcim_subnets')->where('id', $subnetId)->delete();
            \Illuminate\Database\Capsule\Manager::table('dcim_locations')->where('id', $locationId)->delete();
            
            echo "✓ Test data cleaned up\n";
            
        } catch (\Exception $e) {
            echo "✗ Error testing constraints: " . $e->getMessage() . "\n";
        }
        
        echo "\n=========================\n";
        echo "Database schema test completed successfully!\n";
        echo "You can now activate the DCIM module in WHMCS.\n";
        
    } else {
        echo "✗ Error creating database tables: " . $result['error'] . "\n";
        exit(1);
    }
    
} catch (\Exception $e) {
    echo "✗ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
