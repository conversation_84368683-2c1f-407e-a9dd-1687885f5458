<?php
/**
 * DCIM WHMCS Integration Test
 * 
 * Access this via WHMCS admin: /modules/addons/dcim/whmcs_test.php
 * This will test the module in the actual WHMCS environment
 */

// Include WHMCS
$whmcsPath = dirname(dirname(dirname(__DIR__)));
require_once $whmcsPath . '/init.php';

// Check if user is logged in as admin
if (!isset($_SESSION['adminid'])) {
    die('Please log in to WHMCS admin first, then access this page.');
}

echo "<h1>DCIM WHMCS Integration Test</h1>";
echo "<p>Admin ID: " . $_SESSION['adminid'] . "</p>";

// Test module activation status
echo "<h2>Module Status Check</h2>";

try {
    $moduleStatus = \Illuminate\Database\Capsule\Manager::table('tbladdonmodules')
        ->where('module', 'dcim')
        ->get();
    
    if ($moduleStatus->count() > 0) {
        echo "✓ Module is activated in WHMCS<br>";
        echo "<strong>Module Settings:</strong><br>";
        foreach ($moduleStatus as $setting) {
            echo "- " . $setting->setting . ": " . $setting->value . "<br>";
        }
    } else {
        echo "✗ Module is not activated in WHMCS<br>";
        echo "<strong>Action needed:</strong> Go to Setup > Addon Modules and activate DCIM<br>";
    }
} catch (Exception $e) {
    echo "✗ Error checking module status: " . $e->getMessage() . "<br>";
}

// Test admin permissions
echo "<h2>Permission Check</h2>";

try {
    $adminRole = \Illuminate\Database\Capsule\Manager::table('tbladmins')
        ->where('id', $_SESSION['adminid'])
        ->value('roleid');
    
    echo "Admin Role ID: " . ($adminRole ?: 'Full Administrator') . "<br>";
    
    // Check module permissions
    $hasPermission = \Illuminate\Database\Capsule\Manager::table('tbladdonmodules')
        ->where('module', 'dcim')
        ->where('setting', 'access')
        ->where('value', 'like', '%' . ($adminRole ?: '1') . '%')
        ->exists();
    
    if ($hasPermission || !$adminRole) {
        echo "✓ Admin has permission to access DCIM module<br>";
    } else {
        echo "✗ Admin does not have permission to access DCIM module<br>";
        echo "<strong>Action needed:</strong> Go to Setup > Addon Modules > DCIM > Access Control and grant permission<br>";
    }
    
} catch (Exception $e) {
    echo "✗ Error checking permissions: " . $e->getMessage() . "<br>";
}

// Test module output directly
echo "<h2>Module Output Test</h2>";

try {
    // Include the module
    require_once __DIR__ . '/dcim.php';
    
    // Simulate WHMCS module call
    $moduleVars = [
        'modulelink' => 'addonmodules.php?module=dcim',
        'version' => '1.0.0',
        'LANG' => []
    ];
    
    $output = dcim_output($moduleVars);
    
    if (!empty($output)) {
        echo "✓ Module generates output (" . strlen($output) . " characters)<br>";
        echo "<h3>Module Output Preview:</h3>";
        echo "<div style='border: 2px solid #007bff; padding: 15px; background: #f8f9fa; max-height: 400px; overflow: auto;'>";
        echo $output;
        echo "</div>";
    } else {
        echo "✗ Module returns empty output<br>";
    }
    
} catch (Exception $e) {
    echo "✗ Error testing module output: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Test direct access links
echo "<h2>Direct Access Links</h2>";
echo "<p>Try these direct links (corrected for your WHMCS subdirectory):</p>";
echo "<ul>";
echo "<li><a href='/whmcs/admin/addonmodules.php?module=dcim' target='_blank'>DCIM Dashboard</a></li>";
echo "<li><a href='/whmcs/admin/addonmodules.php?module=dcim&action=locations' target='_blank'>Locations</a></li>";
echo "<li><a href='/whmcs/admin/addonmodules.php?module=dcim&action=racks' target='_blank'>Racks</a></li>";
echo "<li><a href='/whmcs/admin/addonmodules.php?module=dcim&action=servers' target='_blank'>Servers</a></li>";
echo "<li><a href='/whmcs/admin/addonmodules.php?module=dcim&action=ipam' target='_blank'>IP Management</a></li>";
echo "</ul>";

echo "<h2>Troubleshooting Steps</h2>";
echo "<ol>";
echo "<li>Ensure the module is activated: Setup > Addon Modules > DCIM > Activate</li>";
echo "<li>Set permissions: Setup > Addon Modules > DCIM > Access Control</li>";
echo "<li>Clear browser cache</li>";
echo "<li>Try accessing via Addons menu in WHMCS admin</li>";
echo "<li>Check WHMCS error logs if issues persist</li>";
echo "</ol>";
?>
