<?php
/**
 * DCIM Module Installation Script
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

// This file can be run independently to install/update the DCIM module
// Usage: php install.php

if (php_sapi_name() !== 'cli') {
    die('This script must be run from the command line.');
}

echo "DCIM Module Installation Script\n";
echo "===============================\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die("Error: PHP 7.4 or higher is required. Current version: " . PHP_VERSION . "\n");
}

// Check if we're in the correct directory
if (!file_exists('dcim.php')) {
    die("Error: Please run this script from the DCIM module directory.\n");
}

echo "✓ PHP version check passed (" . PHP_VERSION . ")\n";
echo "✓ Module files found\n";

// Check file permissions
$directories = ['assets', 'lib', 'templates', 'hooks', 'lang'];
$permissionErrors = [];

foreach ($directories as $dir) {
    if (!is_readable($dir)) {
        $permissionErrors[] = $dir . ' is not readable';
    }
    if (!is_writable($dir)) {
        $permissionErrors[] = $dir . ' is not writable';
    }
}

if (!empty($permissionErrors)) {
    echo "Warning: Permission issues detected:\n";
    foreach ($permissionErrors as $error) {
        echo "  - " . $error . "\n";
    }
    echo "\nPlease fix permissions and try again.\n";
    echo "Recommended: chmod -R 755 " . __DIR__ . "\n\n";
}

echo "✓ File permissions check completed\n";

// Validate module structure
$requiredFiles = [
    'dcim.php',
    'lib/Database.php',
    'lib/AdminController.php',
    'lib/Controllers/DashboardController.php',
    'lib/Controllers/LocationController.php',
    'lib/Controllers/RackController.php',
    'lib/Controllers/ServerController.php',
    'lib/Controllers/IPAMController.php',
    'lib/Controllers/APIController.php',
    'assets/css/dcim.css',
    'assets/js/dcim.js',
    'hooks/dcim_hooks.php',
    'lang/english.php'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "Error: Missing required files:\n";
    foreach ($missingFiles as $file) {
        echo "  - " . $file . "\n";
    }
    die("\nPlease ensure all module files are present.\n");
}

echo "✓ Module structure validation passed\n";

// Check for WHMCS
$whmcsPath = dirname(dirname(dirname(__DIR__)));
$whmcsConfigFile = $whmcsPath . '/configuration.php';

if (!file_exists($whmcsConfigFile)) {
    echo "Warning: WHMCS configuration file not found at expected location.\n";
    echo "Expected: " . $whmcsConfigFile . "\n";
    echo "Please ensure this module is installed in the correct WHMCS directory.\n";
    echo "Correct path: /path/to/whmcs/modules/addons/dcim/\n\n";
} else {
    echo "✓ WHMCS installation detected\n";
}

// Create sample configuration
$sampleConfig = [
    'enable_ipam' => 'on',
    'enable_rack_visualization' => 'on',
    'default_rack_height' => '42',
    'api_access' => 'on',
    'auto_ip_allocation' => 'off'
];

echo "\nRecommended Module Configuration:\n";
foreach ($sampleConfig as $setting => $value) {
    echo "  - " . ucwords(str_replace('_', ' ', $setting)) . ": " . $value . "\n";
}

echo "\nInstallation Steps:\n";
echo "==================\n";
echo "1. Log into your WHMCS admin area\n";
echo "2. Navigate to Setup > Addon Modules\n";
echo "3. Find 'DCIM - Data Center Infrastructure Management'\n";
echo "4. Click 'Activate'\n";
echo "5. Click 'Configure' and set your preferences\n";
echo "6. Set appropriate admin role permissions\n";
echo "7. Access the module via Addons > DCIM\n";

echo "\nPost-Installation:\n";
echo "==================\n";
echo "1. Create your first location\n";
echo "2. Add racks to the location\n";
echo "3. Create IP subnets for your network\n";
echo "4. Add servers and assign them to racks\n";
echo "5. Configure WHMCS service integration\n";

echo "\nAPI Access:\n";
echo "===========\n";
echo "If you enabled API access, endpoints will be available at:\n";
echo "https://your-whmcs-domain.com/api/dcim/\n";
echo "\nAPI documentation is available in the module at:\n";
echo "Addons > DCIM > API\n";

echo "\nTroubleshooting:\n";
echo "================\n";
echo "- Check WHMCS error logs if activation fails\n";
echo "- Ensure database user has CREATE/ALTER permissions\n";
echo "- Verify PHP extensions: PDO, JSON, cURL\n";
echo "- Clear browser cache if interface issues occur\n";

echo "\nSupport:\n";
echo "========\n";
echo "- Documentation: See README.md\n";
echo "- API Docs: Available in module admin interface\n";
echo "- Module Version: 1.0.0\n";

echo "\n✓ Installation script completed successfully!\n";
echo "You can now activate the module in WHMCS admin area.\n\n";
?>
