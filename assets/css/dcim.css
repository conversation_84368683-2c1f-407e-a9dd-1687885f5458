/**
 * DCIM Module CSS Styles
 *
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

/* Bootstrap-like Grid System for WHMCS */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-3, .col-md-4, .col-md-6, .col-md-8, .col-md-12 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-md-3 { flex: 0 0 25%; max-width: 25%; }
.col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-md-6 { flex: 0 0 50%; max-width: 50%; }
.col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-md-12 { flex: 0 0 100%; max-width: 100%; }

/* Bootstrap-like Card System */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.25rem;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
    border-top-left-radius: calc(0.25rem - 1px);
    border-top-right-radius: calc(0.25rem - 1px);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

/* Bootstrap-like Button System */
.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    text-decoration: none;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
    text-decoration: none;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b;
}

.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-warning:hover {
    color: #212529;
    background-color: #e0a800;
    border-color: #d39e00;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* Bootstrap-like Utility Classes */
.mt-4 { margin-top: 1.5rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.text-white { color: #fff !important; }
.text-muted { color: #6c757d !important; }
.text-success { color: #28a745 !important; }
.list-unstyled { padding-left: 0; list-style: none; }

/* Background Colors */
.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-danger { background-color: #dc3545 !important; }

/* Dashboard Specific Styles */
.dcim-dashboard {
    padding: 20px;
}

.dcim-dashboard h1 {
    color: #333;
    margin-bottom: 30px;
    font-size: 2rem;
    font-weight: 300;
}

.dcim-dashboard .card h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

.dcim-dashboard .card p {
    margin-bottom: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.dcim-dashboard h5 {
    margin-bottom: 15px;
    font-weight: 500;
}

/* Flexbox utilities */
.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

/* Alert styles */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Table styles */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}

/* Badge styles */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary {
    color: #fff;
    background-color: #007bff;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-info {
    color: #fff;
    background-color: #17a2b8;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

.badge-danger {
    color: #fff;
    background-color: #dc3545;
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Responsive design */
@media (max-width: 768px) {
    .col-md-3, .col-md-4, .col-md-6, .col-md-8 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 15px;
    }

    .dcim-dashboard {
        padding: 10px;
    }

    .dcim-dashboard h1 {
        font-size: 1.5rem;
    }

    .btn {
        margin-bottom: 5px;
    }
}

.dcim-dashboard .card-body h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

.dcim-dashboard .card-body p {
    margin-bottom: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Sidebar Styles */
.dcim-sidebar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.dcim-sidebar h3 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: #495057;
}

.dcim-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dcim-sidebar ul li {
    margin-bottom: 8px;
}

.dcim-sidebar ul li a {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
}

.dcim-sidebar ul li a:hover {
    text-decoration: underline;
}

/* Table Styles */
.table-responsive {
    border-radius: 5px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
}

/* Form Styles */
.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Button Styles */
.btn {
    border-radius: 4px;
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Rack Visualization Styles */
.rack-visual {
    border: 2px solid #333;
    background: #f8f9fa;
    margin: 20px 0;
    position: relative;
    min-height: 400px;
}

.rack-header {
    background: #333;
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

.rack-units {
    position: relative;
    height: 100%;
}

.rack-unit {
    height: 20px;
    border-bottom: 1px solid #ddd;
    position: relative;
    display: flex;
    align-items: center;
}

.rack-unit-number {
    position: absolute;
    left: -30px;
    font-size: 0.8rem;
    color: #666;
    width: 25px;
    text-align: right;
}

.rack-unit.occupied {
    background: #007bff;
    color: white;
}

.rack-unit.occupied .server-info {
    padding: 0 10px;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.rack-unit.maintenance {
    background: #ffc107;
    color: #212529;
}

.rack-unit.reserved {
    background: #6c757d;
    color: white;
}

/* Server Status Indicators */
.server-status {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.server-status.active {
    background: #28a745;
}

.server-status.maintenance {
    background: #ffc107;
}

.server-status.provisioning {
    background: #17a2b8;
}

.server-status.decommissioned {
    background: #dc3545;
}

/* IP Address Management Styles */
.ip-subnet {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 15px;
}

.ip-subnet-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
}

.ip-subnet-body {
    padding: 15px;
}

.ip-address {
    display: inline-block;
    padding: 2px 6px;
    margin: 2px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-family: monospace;
}

.ip-address.available {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ip-address.assigned {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.ip-address.reserved {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.ip-address.blacklisted {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Switch Port Visualization */
.switch-ports {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 5px;
    margin: 15px 0;
}

.switch-port {
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    font-size: 0.8rem;
    background: #f8f9fa;
}

.switch-port.active {
    border-color: #28a745;
    background: #d4edda;
}

.switch-port.inactive {
    border-color: #6c757d;
    background: #e2e3e5;
}

.switch-port.disabled {
    border-color: #dc3545;
    background: #f8d7da;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dcim-dashboard .card-body h4 {
        font-size: 2rem;
    }
    
    .rack-visual {
        min-height: 300px;
    }
    
    .rack-unit {
        height: 15px;
    }
    
    .switch-ports {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    }
}

/* Alert Styles */
.alert {
    border-radius: 5px;
    margin-bottom: 15px;
}

/* Loading Spinner */
.dcim-loading {
    text-align: center;
    padding: 40px;
}

.dcim-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.stat-card h3 {
    font-size: 2.5rem;
    margin-bottom: 5px;
}

.stat-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Navigation Breadcrumbs */
.dcim-breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 20px;
}

.dcim-breadcrumb .breadcrumb-item {
    font-size: 0.9rem;
}

.dcim-breadcrumb .breadcrumb-item.active {
    color: #6c757d;
}
