/**
 * DCIM Module CSS Styles
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

/* Dashboard Styles */
.dcim-dashboard .card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dcim-dashboard .card-body h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

.dcim-dashboard .card-body p {
    margin-bottom: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Sidebar Styles */
.dcim-sidebar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.dcim-sidebar h3 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: #495057;
}

.dcim-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dcim-sidebar ul li {
    margin-bottom: 8px;
}

.dcim-sidebar ul li a {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
}

.dcim-sidebar ul li a:hover {
    text-decoration: underline;
}

/* Table Styles */
.table-responsive {
    border-radius: 5px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
}

/* Form Styles */
.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Button Styles */
.btn {
    border-radius: 4px;
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Rack Visualization Styles */
.rack-visual {
    border: 2px solid #333;
    background: #f8f9fa;
    margin: 20px 0;
    position: relative;
    min-height: 400px;
}

.rack-header {
    background: #333;
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

.rack-units {
    position: relative;
    height: 100%;
}

.rack-unit {
    height: 20px;
    border-bottom: 1px solid #ddd;
    position: relative;
    display: flex;
    align-items: center;
}

.rack-unit-number {
    position: absolute;
    left: -30px;
    font-size: 0.8rem;
    color: #666;
    width: 25px;
    text-align: right;
}

.rack-unit.occupied {
    background: #007bff;
    color: white;
}

.rack-unit.occupied .server-info {
    padding: 0 10px;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.rack-unit.maintenance {
    background: #ffc107;
    color: #212529;
}

.rack-unit.reserved {
    background: #6c757d;
    color: white;
}

/* Server Status Indicators */
.server-status {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.server-status.active {
    background: #28a745;
}

.server-status.maintenance {
    background: #ffc107;
}

.server-status.provisioning {
    background: #17a2b8;
}

.server-status.decommissioned {
    background: #dc3545;
}

/* IP Address Management Styles */
.ip-subnet {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 15px;
}

.ip-subnet-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
}

.ip-subnet-body {
    padding: 15px;
}

.ip-address {
    display: inline-block;
    padding: 2px 6px;
    margin: 2px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-family: monospace;
}

.ip-address.available {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ip-address.assigned {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.ip-address.reserved {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.ip-address.blacklisted {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Switch Port Visualization */
.switch-ports {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 5px;
    margin: 15px 0;
}

.switch-port {
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    font-size: 0.8rem;
    background: #f8f9fa;
}

.switch-port.active {
    border-color: #28a745;
    background: #d4edda;
}

.switch-port.inactive {
    border-color: #6c757d;
    background: #e2e3e5;
}

.switch-port.disabled {
    border-color: #dc3545;
    background: #f8d7da;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dcim-dashboard .card-body h4 {
        font-size: 2rem;
    }
    
    .rack-visual {
        min-height: 300px;
    }
    
    .rack-unit {
        height: 15px;
    }
    
    .switch-ports {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    }
}

/* Alert Styles */
.alert {
    border-radius: 5px;
    margin-bottom: 15px;
}

/* Loading Spinner */
.dcim-loading {
    text-align: center;
    padding: 40px;
}

.dcim-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.stat-card h3 {
    font-size: 2.5rem;
    margin-bottom: 5px;
}

.stat-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Navigation Breadcrumbs */
.dcim-breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 20px;
}

.dcim-breadcrumb .breadcrumb-item {
    font-size: 0.9rem;
}

.dcim-breadcrumb .breadcrumb-item.active {
    color: #6c757d;
}
