/**
 * DCIM Module JavaScript
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 */

(function($) {
    'use strict';

    // Initialize DCIM module
    var DCIM = {
        
        init: function() {
            this.bindEvents();
            this.initializeComponents();
        },
        
        bindEvents: function() {
            // Confirm delete actions
            $(document).on('click', '.btn-danger[href*="delete"]', function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Auto-refresh dashboard stats
            if ($('.dcim-dashboard').length > 0) {
                this.initDashboardRefresh();
            }
            
            // Initialize rack visualization
            if ($('.rack-visual').length > 0) {
                this.initRackVisualization();
            }
            
            // Initialize IP address management
            if ($('.ip-subnet').length > 0) {
                this.initIPAM();
            }
            
            // Initialize switch port management
            if ($('.switch-ports').length > 0) {
                this.initSwitchPorts();
            }
        },
        
        initializeComponents: function() {
            // Initialize tooltips
            if (typeof $().tooltip === 'function') {
                $('[data-toggle="tooltip"]').tooltip();
            }
            
            // Initialize popovers
            if (typeof $().popover === 'function') {
                $('[data-toggle="popover"]').popover();
            }
            
            // Initialize form validation
            this.initFormValidation();
        },
        
        initDashboardRefresh: function() {
            // Auto-refresh dashboard every 5 minutes
            setInterval(function() {
                if ($('.dcim-dashboard').length > 0) {
                    DCIM.refreshDashboardStats();
                }
            }, 300000); // 5 minutes
        },
        
        refreshDashboardStats: function() {
            // AJAX call to refresh dashboard statistics
            $.ajax({
                url: 'addonmodules.php?module=dcim&action=dashboard&ajax=stats',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.success) {
                        // Update statistics cards
                        $('.dcim-dashboard .card-body h4').each(function(index) {
                            var statValue = data.stats[Object.keys(data.stats)[index]];
                            $(this).text(statValue);
                        });
                    }
                },
                error: function() {
                    console.log('Failed to refresh dashboard statistics');
                }
            });
        },
        
        initRackVisualization: function() {
            $('.rack-unit').hover(
                function() {
                    // Show server details on hover
                    var serverInfo = $(this).data('server-info');
                    if (serverInfo) {
                        $(this).attr('title', serverInfo);
                    }
                },
                function() {
                    // Hide tooltip
                    $(this).removeAttr('title');
                }
            );
            
            // Click to edit server assignment
            $('.rack-unit').click(function() {
                var rackId = $(this).closest('.rack-visual').data('rack-id');
                var unitNumber = $(this).data('unit-number');
                
                if ($(this).hasClass('occupied')) {
                    // Edit existing server
                    var serverId = $(this).data('server-id');
                    window.location.href = 'addonmodules.php?module=dcim&action=servers&sub=edit&id=' + serverId;
                } else {
                    // Assign new server
                    window.location.href = 'addonmodules.php?module=dcim&action=servers&sub=create&rack_id=' + rackId + '&unit=' + unitNumber;
                }
            });
        },
        
        initIPAM: function() {
            // IP address click handlers
            $('.ip-address').click(function() {
                var ipAddress = $(this).text();
                var subnetId = $(this).closest('.ip-subnet').data('subnet-id');
                
                if ($(this).hasClass('available')) {
                    // Assign IP
                    DCIM.showIPAssignmentModal(ipAddress, subnetId);
                } else if ($(this).hasClass('assigned')) {
                    // Edit assignment
                    var assignmentId = $(this).data('assignment-id');
                    window.location.href = 'addonmodules.php?module=dcim&action=ipam&sub=edit_assignment&id=' + assignmentId;
                }
            });
            
            // Subnet utilization bars
            $('.ip-subnet').each(function() {
                var total = $(this).data('total-ips');
                var used = $(this).data('used-ips');
                var utilization = (used / total) * 100;
                
                var progressBar = '<div class="progress mt-2" style="height: 5px;">';
                progressBar += '<div class="progress-bar" role="progressbar" style="width: ' + utilization + '%"></div>';
                progressBar += '</div>';
                
                $(this).find('.ip-subnet-header').append(progressBar);
            });
        },
        
        showIPAssignmentModal: function(ipAddress, subnetId) {
            // Create modal for IP assignment
            var modal = $('<div class="modal fade" tabindex="-1">');
            modal.html(`
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Assign IP Address: ${ipAddress}</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="ip-assignment-form">
                                <input type="hidden" name="ip_address" value="${ipAddress}">
                                <input type="hidden" name="subnet_id" value="${subnetId}">
                                
                                <div class="form-group">
                                    <label>Assignment Type</label>
                                    <select name="assignment_type" class="form-control" required>
                                        <option value="server">Server</option>
                                        <option value="switch">Switch</option>
                                        <option value="client">Client</option>
                                        <option value="service">Service</option>
                                        <option value="reserved">Reserved</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label>Hostname</label>
                                    <input type="text" name="hostname" class="form-control">
                                </div>
                                
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea name="description" class="form-control" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="DCIM.submitIPAssignment()">Assign IP</button>
                        </div>
                    </div>
                </div>
            `);
            
            $('body').append(modal);
            modal.modal('show');
            
            // Remove modal after hiding
            modal.on('hidden.bs.modal', function() {
                modal.remove();
            });
        },
        
        submitIPAssignment: function() {
            var formData = $('#ip-assignment-form').serialize();
            
            $.ajax({
                url: 'addonmodules.php?module=dcim&action=ipam&sub=assign_ip',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload(); // Refresh page to show updated IP assignments
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('Failed to assign IP address');
                }
            });
        },
        
        initSwitchPorts: function() {
            // Switch port click handlers
            $('.switch-port').click(function() {
                var switchId = $(this).closest('.switch-ports').data('switch-id');
                var portNumber = $(this).data('port-number');
                
                window.location.href = 'addonmodules.php?module=dcim&action=switches&sub=edit_port&switch_id=' + switchId + '&port=' + portNumber;
            });
            
            // Port status indicators
            $('.switch-port').each(function() {
                var status = $(this).data('status');
                $(this).addClass(status);
            });
        },
        
        initFormValidation: function() {
            // Custom form validation
            $('form').on('submit', function(e) {
                var form = $(this);
                var isValid = true;
                
                // Check required fields
                form.find('[required]').each(function() {
                    if (!$(this).val()) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                // Validate IP addresses
                form.find('input[type="text"][name*="ip"]').each(function() {
                    var value = $(this).val();
                    if (value && !DCIM.isValidIP(value)) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
            });
        },
        
        isValidIP: function(ip) {
            // Basic IP validation
            var ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            return ipRegex.test(ip);
        },
        
        showLoading: function(element) {
            var spinner = '<div class="dcim-loading"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>';
            $(element).html(spinner);
        },
        
        hideLoading: function(element) {
            $(element).find('.dcim-loading').remove();
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        DCIM.init();
    });

    // Make DCIM object globally available
    window.DCIM = DCIM;

})(jQuery);
