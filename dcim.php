<?php
/**
 * <PERSON><PERSON> (Data Center Infrastructure Management) Addon Module for WHMCS
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 * @license    MIT License
 * @version    1.0.0
 * @link       https://github.com/dcim/whmcs-dcim
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Module Configuration
 */
function dcim_config() {
    return [
        'name' => 'DCIM - Data Center Infrastructure Management',
        'description' => 'Comprehensive data center infrastructure management system for WHMCS',
        'version' => '1.0.0',
        'author' => 'DCIM Development Team',
        'language' => 'english',
        'fields' => [
            'enable_ipam' => [
                'FriendlyName' => 'Enable IPAM',
                'Type' => 'yesno',
                'Description' => 'Enable IP Address Management functionality',
                'Default' => 'on',
            ],
            'enable_rack_visualization' => [
                'FriendlyName' => 'Enable Rack Visualization',
                'Type' => 'yesno',
                'Description' => 'Enable visual rack layout representation',
                'Default' => 'on',
            ],
            'default_rack_height' => [
                'FriendlyName' => 'Default Rack Height (U)',
                'Type' => 'text',
                'Size' => '5',
                'Default' => '42',
                'Description' => 'Default rack height in U units',
            ],
            'api_access' => [
                'FriendlyName' => 'Enable API Access',
                'Type' => 'yesno',
                'Description' => 'Enable REST API endpoints for external integrations',
                'Default' => 'on',
            ],
            'auto_ip_allocation' => [
                'FriendlyName' => 'Auto IP Allocation',
                'Type' => 'yesno',
                'Description' => 'Automatically allocate IPs when creating new services',
                'Default' => 'off',
            ],
        ]
    ];
}

/**
 * Module Activation
 */
function dcim_activate() {
    try {
        // Include database migration functions
        require_once __DIR__ . '/lib/Database.php';
        
        $database = new DCIM\Database();
        $result = $database->createTables();
        
        if ($result['success']) {
            return [
                'status' => 'success',
                'description' => 'DCIM module activated successfully. Database tables created.'
            ];
        } else {
            return [
                'status' => 'error',
                'description' => 'Failed to create database tables: ' . $result['error']
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'description' => 'Module activation failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Module Deactivation
 */
function dcim_deactivate() {
    // Note: We don't drop tables on deactivation to preserve data
    return [
        'status' => 'success',
        'description' => 'DCIM module deactivated successfully. Data preserved.'
    ];
}

/**
 * Module Output - Main Admin Interface
 */
function dcim_output($vars) {
    // Always return visible content for debugging
    return '<div style="background: #ff0000; color: white; padding: 30px; margin: 20px; border: 5px solid #000; font-size: 18px; font-weight: bold;">
            <h1 style="color: white; margin: 0 0 20px 0;">🔴 DCIM MODULE IS WORKING! 🔴</h1>
            <p style="margin: 10px 0;">This red box proves WHMCS is calling the dcim_output() function.</p>
            <p style="margin: 10px 0;">Module Path: ' . __DIR__ . '</p>
            <p style="margin: 10px 0;">Current Action: ' . ($_GET['action'] ?? 'dashboard') . '</p>
            <p style="margin: 10px 0;">WHMCS Version: ' . (isset($vars['whmcsVersion']) ? $vars['whmcsVersion'] : 'Unknown') . '</p>
            <hr style="margin: 20px 0; border: 2px solid white;">
            <p style="margin: 10px 0;"><strong>If you can see this, the module is properly loaded!</strong></p>
            </div>';
}

/**
 * Admin Area Sidebar
 */
function dcim_sidebar($vars) {
    $sidebar = '<div class="dcim-sidebar">';
    $sidebar .= '<h3>DCIM Quick Actions</h3>';
    $sidebar .= '<ul>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=locations">Manage Locations</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=racks">Manage Racks</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=servers">Manage Servers</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=switches">Manage Switches</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=ipam">IP Management</a></li>';
    $sidebar .= '</ul>';
    $sidebar .= '</div>';
    
    return $sidebar;
}

/**
 * Client Area Output (if needed for future client-facing features)
 */
function dcim_clientarea($vars) {
    // Future implementation for client-facing features
    return '';
}
