<?php
/**
 * <PERSON><PERSON> (Data Center Infrastructure Management) Addon Module for WHMCS
 * 
 * @package    DCIM
 * <AUTHOR> Development Team
 * @copyright  2024 DCIM Solutions
 * @license    MIT License
 * @version    1.0.0
 * @link       https://github.com/dcim/whmcs-dcim
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Module Configuration
 */
function dcim_config() {
    return [
        'name' => 'DCIM - Data Center Infrastructure Management',
        'description' => 'Comprehensive data center infrastructure management system for WHMCS',
        'version' => '1.0.0',
        'author' => 'DCIM Development Team',
        'language' => 'english',
        'fields' => [
            'enable_ipam' => [
                'FriendlyName' => 'Enable IPAM',
                'Type' => 'yesno',
                'Description' => 'Enable IP Address Management functionality',
                'Default' => 'on',
            ],
            'enable_rack_visualization' => [
                'FriendlyName' => 'Enable Rack Visualization',
                'Type' => 'yesno',
                'Description' => 'Enable visual rack layout representation',
                'Default' => 'on',
            ],
            'default_rack_height' => [
                'FriendlyName' => 'Default Rack Height (U)',
                'Type' => 'text',
                'Size' => '5',
                'Default' => '42',
                'Description' => 'Default rack height in U units',
            ],
            'api_access' => [
                'FriendlyName' => 'Enable API Access',
                'Type' => 'yesno',
                'Description' => 'Enable REST API endpoints for external integrations',
                'Default' => 'on',
            ],
            'auto_ip_allocation' => [
                'FriendlyName' => 'Auto IP Allocation',
                'Type' => 'yesno',
                'Description' => 'Automatically allocate IPs when creating new services',
                'Default' => 'off',
            ],
        ]
    ];
}

/**
 * Module Activation
 */
function dcim_activate() {
    try {
        // Include database migration functions
        require_once __DIR__ . '/lib/Database.php';
        
        $database = new DCIM\Database();
        $result = $database->createTables();
        
        if ($result['success']) {
            return [
                'status' => 'success',
                'description' => 'DCIM module activated successfully. Database tables created.'
            ];
        } else {
            return [
                'status' => 'error',
                'description' => 'Failed to create database tables: ' . $result['error']
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'description' => 'Module activation failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Module Deactivation
 */
function dcim_deactivate() {
    // Note: We don't drop tables on deactivation to preserve data
    return [
        'status' => 'success',
        'description' => 'DCIM module deactivated successfully. Data preserved.'
    ];
}

/**
 * Module Output - Main Admin Interface
 */
function dcim_output($vars) {
    // Simple test to ensure WHMCS is rendering module output
    $testMode = isset($_GET['test']) && $_GET['test'] == '1';

    if ($testMode) {
        return '<div style="background: red; color: white; padding: 20px; margin: 20px; border: 3px solid black;">
                <h1>DCIM TEST MODE</h1>
                <p>If you can see this red box, WHMCS is properly rendering module output.</p>
                <p>Module vars: ' . print_r($vars, true) . '</p>
                <p>GET params: ' . print_r($_GET, true) . '</p>
                </div>';
    }

    try {
        // Debug: Check if files exist
        $adminControllerPath = __DIR__ . '/lib/AdminController.php';
        if (!file_exists($adminControllerPath)) {
            return '<div style="background: yellow; padding: 20px; border: 2px solid red;">Error: AdminController.php not found at: ' . $adminControllerPath . '</div>';
        }

        // Include the main admin controller
        require_once $adminControllerPath;

        // Check if class exists
        if (!class_exists('DCIM\AdminController')) {
            return '<div style="background: yellow; padding: 20px; border: 2px solid red;">Error: DCIM\AdminController class not found</div>';
        }

        $controller = new DCIM\AdminController($vars);
        $output = $controller->handleRequest();

        // Debug: Check if output is empty
        if (empty($output)) {
            return '<div style="background: orange; padding: 20px; border: 2px solid red;">Debug: Controller returned empty output. Action: ' . ($_GET['action'] ?? 'none') . '</div>';
        }

        // Add a visible wrapper to ensure content is displayed
        return '<div style="min-height: 100px; background: #f9f9f9; padding: 10px;">' . $output . '</div>';

    } catch (Exception $e) {
        return '<div style="background: red; color: white; padding: 20px;">DCIM Error: ' . htmlspecialchars($e->getMessage()) . '<br><pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre></div>';
    } catch (Error $e) {
        return '<div style="background: red; color: white; padding: 20px;">DCIM Fatal Error: ' . htmlspecialchars($e->getMessage()) . '<br><pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre></div>';
    }
}

/**
 * Admin Area Sidebar
 */
function dcim_sidebar($vars) {
    $sidebar = '<div class="dcim-sidebar">';
    $sidebar .= '<h3>DCIM Quick Actions</h3>';
    $sidebar .= '<ul>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=locations">Manage Locations</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=racks">Manage Racks</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=servers">Manage Servers</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=switches">Manage Switches</a></li>';
    $sidebar .= '<li><a href="addonmodules.php?module=dcim&action=ipam">IP Management</a></li>';
    $sidebar .= '</ul>';
    $sidebar .= '</div>';
    
    return $sidebar;
}

/**
 * Client Area Output (if needed for future client-facing features)
 */
function dcim_clientarea($vars) {
    // Future implementation for client-facing features
    return '';
}
